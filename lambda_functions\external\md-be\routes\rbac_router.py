from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from controller.rbac_controller import RBACController
from db.db import get_db
from dependencies.admin_jwt import validate_jwt_token
from schemas.rbac import *
from typing import List

router = APIRouter()
rbacController = RBACController()

# ========================
# ROLE ENDPOINTS
# ========================

@router.post("/roles", status_code=status.HTTP_201_CREATED)
async def create_role(role: RoleCreate, user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Create a new role"""
    return rbacController.create_role(role, user, db)

@router.get("/roles")
async def get_all_roles(
    user = Depends(validate_jwt_token),
    db: Session = Depends(get_db),
    perPage: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Get all roles"""
    return rbacController.get_all_roles(db, perPage=perPage, pageSize=pageSize)

@router.get("/roles/{role_uuid}")
async def get_role_by_uuid(role_uuid: str, user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Get a role by uuid"""
    return rbacController.get_role_by_uuid(role_uuid, db)

@router.put("/roles/{role_uuid}")
async def update_role(role_uuid: str, role_update: RoleUpdate, user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Update a role"""
    return rbacController.update_role(role_uuid, role_update, user, db)

@router.delete("/roles/{role_uuid}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(role_uuid: str, user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Delete a role"""
    return rbacController.delete_role(role_uuid, db)

# ========================
# MODULE ENDPOINTS
# ========================

@router.post("/modules", status_code=status.HTTP_201_CREATED)
async def create_module(module: ModuleCreate, user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Create a new module"""
    return rbacController.create_module(module, user, db)

@router.get("/modules")
async def get_all_modules(user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Get all modules"""
    return rbacController.get_all_modules(db)

@router.get("/modules/{uuid}")
async def get_module_by_uuid(uuid: str, user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Get a module by ID"""
    return rbacController.get_module_by_id(uuid, db)

@router.put("/modules/{uuid}")
async def update_module(uuid: str, module_update: ModuleUpdate, user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Update a module"""
    return rbacController.update_module(uuid, module_update, user, db)

@router.delete("/modules/{uuid}")
async def delete_module(uuid: str, user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Delete a module"""
    return rbacController.delete_module(uuid, db)

# ========================
# ROLE MODULE PERMISSION ENDPOINTS
# ========================


@router.get("/permissions")
async def get_all_role_module_permissions(user = Depends(validate_jwt_token), db: Session = Depends(get_db)):
    """Get all role-module permissions grouped by role"""
    return rbacController.get_all_role_module_permissions(db)

# recieving role and module uuid in query param to delete permission
@router.delete("/permissions")
async def delete_role_module_permission(
        user = Depends(validate_jwt_token),
        role_uuid: str = Query(...),
        module_uuid: str = Query(...),
        db: Session = Depends(get_db)
    ):
    """Delete role-module permission"""
    
    return rbacController.delete_role_module_permission(role_uuid, module_uuid, db)

# ========================
# ADVANCED RBAC ENDPOINTS
# ========================

@router.post("/roles/upsert-with-permissions")
async def upsert_role_with_permissions(
    request: RoleUpsertWithPermissionsRequest,
    user = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Create or update a role with module-specific permissions"""
    request_data = request.model_dump()
    return rbacController.upsert_role_with_permissions(request_data, user, db)
