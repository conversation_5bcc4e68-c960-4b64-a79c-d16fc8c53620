from fastapi import Security
from fastapi.security import HTTPAuthorizationCredentials, HTT<PERSON><PERSON>earer
from config import settings
from utils.jwt_validator import validate_jwt_token as shared_validate

security_scheme = HTTPBearer()

def validate_jwt_token(credentials: HTTPAuthorizationCredentials = Security(security_scheme)) -> dict:
    token = credentials.credentials
    return shared_validate(
        token,
        settings.COGNITO_JWKS_URL,
        settings.COGNITO_ISSUER,
        settings.COGNITO_AUDIENCE
    )
