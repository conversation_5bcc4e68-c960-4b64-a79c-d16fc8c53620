import datetime
import jwt
import hashlib
from typing import Dict, Optional
from config import settings
import logging

logger = logging.getLogger(__name__)

# Secret key for custom JWT tokens - should be stored in environment variables
CUSTOM_JWT_SECRET_KEY = "your-custom-jwt-secret-key-here"  # TODO: Move to environment variables
CUSTOM_JWT_ALGORITHM = "HS256"

def generate_password_change_token(username: str, expires_in_hours: int = 24) -> str:
    """
    Generate a custom JWT token for password change that expires in specified hours
    """
    try:
        payload = {
            "username": username,
            "type": "password_change",
            "exp": datetime.datetime.utcnow() + datetime.timedelta(hours=expires_in_hours),
            "iat": datetime.datetime.utcnow()
        }
        
        token = jwt.encode(payload, CUSTOM_JWT_SECRET_KEY, algorithm=CUSTOM_JWT_ALGORITHM)
        return token
        
    except Exception as e:
        logger.error(f"Error generating password change token: {str(e)}")
        raise Exception(f"Failed to generate token: {str(e)}")

def validate_password_change_token(token: str) -> Optional[Dict]:
    """
    Validate and decode a password change token
    Returns the payload if valid, None if invalid
    """
    try:
        payload = jwt.decode(token, CUSTOM_JWT_SECRET_KEY, algorithms=[CUSTOM_JWT_ALGORITHM])
        
        # Check if it's a password change token
        if payload.get("type") != "password_change":
            logger.warning("Invalid token type")
            return None
            
        # Check if token has expired
        exp_timestamp = payload.get("exp")
        if exp_timestamp and datetime.datetime.utcnow().timestamp() > exp_timestamp:
            logger.warning("Token has expired")
            return None
            
        return payload
        
    except jwt.ExpiredSignatureError:
        logger.warning("Token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error validating token: {str(e)}")
        return None

def extract_username_from_token(token: str) -> Optional[str]:
    """
    Extract username from a valid password change token
    """
    payload = validate_password_change_token(token)
    if payload:
        return payload.get("username")
    return None

def get_token_hash(token: str) -> str:
    """
    Generate a hash of the token for database storage
    """
    return hashlib.sha256(token.encode()).hexdigest()

def get_token_expiration(token: str) -> Optional[datetime.datetime]:
    """
    Get the expiration time from a token
    """
    payload = validate_password_change_token(token)
    if payload and payload.get("exp"):
        return datetime.datetime.fromtimestamp(payload["exp"])
    return None 