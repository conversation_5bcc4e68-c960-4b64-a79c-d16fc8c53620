from sqlalchemy import Column, Integer, String, DateTime, func, text
from sqlalchemy.dialects.postgresql import JSONB, UUID
from db.db import Base

class CoLog(Base):
    __tablename__ = "co_log"

    id = Column("id", Integer, primary_key=True, autoincrement=True)
    uuid = Column("uuid", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), unique=True, nullable=False)
    timestamp = Column("timestamp", DateTime, default=func.timezone('UTC', func.now()), nullable=False)
    userUuid = Column("userUuid", UUID(as_uuid=True), nullable=False)
    action = Column("action", String(255), nullable=False)
    context = Column("context", JSONB, nullable=True)
    purpose = Column("purpose", String(500), nullable=False)