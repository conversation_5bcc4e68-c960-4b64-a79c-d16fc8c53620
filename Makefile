.PHONY: all all-plan docker-login docker-build docker-push terraform-init terraform-plan terraform-apply

LAMBDA_DIR=./lambda_functions/$(function_path)
AWS_DEFAULT_REGION=us-east-1
AWS_ACCOUNT_ID=$(shell aws sts get-caller-identity --query Account --output text)
ECR_REPO=$(ecr_repo)
REPOSITORY_URI=$(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_DEFAULT_REGION).amazonaws.com/$(ECR_REPO)
DEPLOY_ENVIRONMENT=$(deploy_environment)
COMMIT_HASH=$(shell echo $(CODEBUILD_RESOLVED_SOURCE_VERSION) | cut -c 1-7)-$(DEPLOY_ENVIRONMENT)


docker-login:
	@echo Logging into amazon ecr...
	aws --version
	aws ecr get-login-password --region $(AWS_DEFAULT_REGION) | docker login --username AWS --password-stdin $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_DEFAULT_REGION).amazonaws.com
docker-build:
	@echo deploy env is $(DEPLOY_ENVIRONMENT)
	@echo commit hash is $(COMMIT_HASH) and image tag is $(IMAGE_TAG)
	@echo Build started on `date`
	@echo Building the Docker image...
	docker buildx build --platform linux/amd64 --provenance=false -t $(REPOSITORY_URI):latest $(LAMBDA_DIR)
	docker tag $(REPOSITORY_URI):latest $(REPOSITORY_URI):$(COMMIT_HASH)
docker-push:
	@echo build completed on `date`
	@echo pushing the docker image
	docker push $(REPOSITORY_URI):latest
	docker push $(REPOSITORY_URI):$(COMMIT_HASH)
terraform-init:
	@if echo "$(DEPLOY_ENVIRONMENT)" | grep -q internal; then \
	echo 'internal found in DEPLOY_ENVIRONMENT using internal terraform settings'; \
	mv lambda_apigw_public.tf lambda_apigw_public.tf.unused; \
	else \
	echo 'internal not found in DEPLOY_ENVIRONMENT using external terraform settings'; \
	mv lambda_apigw_internal.tf lambda_apigw_internal.tf.unused; \
	fi
	terraform init -backend-config=environments/backend-$(DEPLOY_ENVIRONMENT).hcl

terraform-plan:
	cp environments/$(DEPLOY_ENVIRONMENT).tfvars $(DEPLOY_ENVIRONMENT).auto.tfvars
	echo "image_uri = \"$(REPOSITORY_URI):$(COMMIT_HASH)\"" > image_uri.auto.tfvars
	terraform plan

terraform-apply:
	terraform apply -auto-approve
all-plan: docker-login docker-build docker-push terraform-init terraform-plan
all: docker-login docker-build docker-push terraform-init terraform-plan terraform-apply
