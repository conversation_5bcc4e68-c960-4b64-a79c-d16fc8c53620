import boto3
from botocore.exceptions import ClientError
from config import settings
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

# Initialize SES client
ses_client = boto3.client(
    'ses',
    region_name="us-east-1"
)

def get_admin_welcome_email_template(admin_data: Dict[str, Any]) -> str:
    """
    Generate a beautiful HTML email template for new admin welcome
    """
    template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {settings.COMPANY_NAME}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }}
        
        .email-container {{
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }}
        
        .logo {{
            max-width: 120px;
            height: auto;
            margin-bottom: 20px;
        }}
        
        .header h1 {{
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }}
        
        .header p {{
            font-size: 16px;
            opacity: 0.9;
        }}
        
        .content {{
            padding: 40px 30px;
        }}
        
        .welcome-message {{
            text-align: center;
            margin-bottom: 35px;
        }}
        
        .welcome-message h2 {{
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 15px;
        }}
        
        .admin-details {{
            background-color: #f8f9fb;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            border-left: 4px solid #667eea;
        }}
        
        .detail-row {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }}
        
        .detail-row:last-child {{
            border-bottom: none;
            margin-bottom: 0;
        }}
        
        .detail-label {{
            font-weight: 600;
            color: #495057;
            min-width: 120px;
        }}
        
        .detail-value {{
            color: #212529;
            font-weight: 500;
        }}
        
        .login-section {{
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 8px;
            text-align: center;
            margin: 30px 0;
        }}
        
        .login-section h3 {{
            margin-bottom: 15px;
            font-size: 20px;
        }}
        
        .login-button {{
            display: inline-block;
            background-color: #ffffff;
            color: #f5576c;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin-top: 15px;
            transition: all 0.3s ease;
        }}
        
        .login-button:hover {{
            background-color: #f8f9fa;
            transform: translateY(-2px);
        }}
        
        .credentials-warning {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }}
        
        .credentials-warning .icon {{
            font-size: 24px;
            margin-bottom: 10px;
        }}
        
        .credentials-warning p {{
            color: #856404;
            font-weight: 500;
        }}
        
        .features {{
            margin: 30px 0;
        }}
        
        .features h3 {{
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }}
        
        .feature-list {{
            list-style: none;
            padding: 0;
        }}
        
        .feature-item {{
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            background-color: #f8f9fb;
            border-radius: 6px;
        }}
        
        .feature-icon {{
            background-color: #667eea;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 12px;
        }}
        
        .footer {{
            background-color: #343a40;
            color: #adb5bd;
            padding: 30px;
            text-align: center;
        }}
        
        .footer p {{
            margin-bottom: 10px;
        }}
        
        .footer a {{
            color: #667eea;
            text-decoration: none;
        }}
        
        @media (max-width: 600px) {{
            .email-container {{
                margin: 10px;
            }}
            
            .header, .content {{
                padding: 25px 20px;
            }}
            
            .detail-row {{
                flex-direction: column;
                gap: 5px;
            }}
            
            .detail-label {{
                min-width: auto;
            }}
        }}
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            {f'<img src="{settings.COMPANY_LOGO_URL}" alt="{settings.COMPANY_NAME}" class="logo">' if settings.COMPANY_LOGO_URL else ''}
            <h1>Welcome to {settings.COMPANY_NAME}</h1>
            <p>Your admin account has been successfully created</p>
        </div>
        
        <div class="content">
            <div class="welcome-message">
                <h2>Hello {admin_data.get('firstName', '')} {admin_data.get('lastName', '')}! 👋</h2>
                <p>We're excited to have you join our administrative team. Your account has been set up and you're ready to get started.</p>
            </div>
            
            <div class="admin-details">
                <div class="detail-row">
                    <span class="detail-label">Username:</span>
                    <span class="detail-value">{admin_data.get('username', '')}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value">{admin_data.get('email', '')}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Full Name:</span>
                    <span class="detail-value">{admin_data.get('firstName', '')} {admin_data.get('lastName', '')}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Role:</span>
                    <span class="detail-value">{', '.join(admin_data.get('roles', ['Administrator']))}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value">✅ Active</span>
                </div>
            </div>
            
            <div class="credentials-warning">
                <div class="icon">🔐</div>
                <p><strong>Important:</strong> Use the username and password provided by your administrator to log in. Please change your password after your first login for security.</p>
            </div>
            
            <div class="login-section">
                <h3>Ready to Get Started?</h3>
                <p>Click the button below to access your admin dashboard</p>
                <a href="{settings.APP_BASE_URL}/login" class="login-button">Login to Dashboard</a>
            </div>
            
            <div class="features">
                <h3>What You Can Do</h3>
                <ul class="feature-list">
                    <li class="feature-item">
                        <span class="feature-icon">👥</span>
                        <span>Manage user accounts and permissions</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">📊</span>
                        <span>Access comprehensive analytics and reports</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">⚙️</span>
                        <span>Configure system settings and preferences</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🔒</span>
                        <span>Monitor security and audit logs</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>{settings.COMPANY_NAME}</strong></p>
            <p>If you have any questions, please contact your system administrator.</p>
            <p>This is an automated message, please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
"""
    return template

def send_admin_welcome_email(admin_data: Dict[str, Any]) -> bool:
    """
    Send welcome email to newly created admin
    """
    try:
        # Generate email content
        html_content = get_admin_welcome_email_template(admin_data)
        
        # Prepare email
        email_data = {
            'Source': f"{settings.EMAIL_FROM_NAME} <{settings.EMAIL_FROM_ADDRESS}>",
            'Destination': {
                'ToAddresses': [admin_data['email']]
            },
            'Message': {
                'Subject': {
                    'Data': f"Welcome to {settings.COMPANY_NAME} - Admin Account Created",
                    'Charset': 'UTF-8'
                },
                'Body': {
                    'Html': {
                        'Data': html_content,
                        'Charset': 'UTF-8'
                    },
                    'Text': {
                        'Data': f"""
Welcome to {settings.COMPANY_NAME}!

Hello {admin_data.get('firstName', '')} {admin_data.get('lastName', '')},

Your admin account has been successfully created with the following details:

Username: {admin_data.get('username', '')}
Email: {admin_data.get('email', '')}
Role: {', '.join(admin_data.get('roles', ['Administrator']))}
Status: Active

Please use the username and password provided by your administrator to log in.

Login URL: {settings.APP_BASE_URL}/login

If you have any questions, please contact your system administrator.

Best regards,
{settings.COMPANY_NAME} Team
                        """,
                        'Charset': 'UTF-8'
                    }
                }
            }
        }
        
        # Send email via SES
        response = ses_client.send_email(**email_data)
        
        logger.info(f"Welcome email sent successfully to {admin_data['email']}. Message ID: {response['MessageId']}")
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_message = e.response['Error']['Message']
        logger.error(f"Failed to send welcome email to {admin_data['email']}: {error_code} - {error_message}")
        return False
        
    except Exception as e:
        logger.error(f"Unexpected error sending welcome email to {admin_data['email']}: {str(e)}")
        return False

def get_admin_welcome_email_with_password_change_template(admin_data: Dict[str, Any]) -> str:
    """
    Generate a beautiful HTML email template for new admin welcome with password change link
    """
    password_change_url = f"{settings.APP_BASE_URL}/change-password?accessToken={admin_data.get('accessToken', '')}"
    
    template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {settings.COMPANY_NAME}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }}
        
        .email-container {{
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }}
        
        .logo {{
            max-width: 120px;
            height: auto;
            margin-bottom: 20px;
        }}
        
        .header h1 {{
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }}
        
        .header p {{
            font-size: 16px;
            opacity: 0.9;
        }}
        
        .content {{
            padding: 40px 30px;
        }}
        
        .welcome-message {{
            text-align: center;
            margin-bottom: 35px;
        }}
        
        .welcome-message h2 {{
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 15px;
        }}
        
        .admin-details {{
            background-color: #f8f9fb;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            border-left: 4px solid #667eea;
        }}
        
        .detail-row {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }}
        
        .detail-row:last-child {{
            border-bottom: none;
            margin-bottom: 0;
        }}
        
        .detail-label {{
            font-weight: 600;
            color: #495057;
            min-width: 120px;
        }}
        
        .detail-value {{
            color: #212529;
            font-weight: 500;
        }}
        
        .password-change-section {{
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 8px;
            text-align: center;
            margin: 30px 0;
        }}
        
        .password-change-section h3 {{
            margin-bottom: 15px;
            font-size: 20px;
        }}
        
        .password-change-button {{
            display: inline-block;
            background-color: #ffffff;
            color: #f5576c;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin-top: 15px;
            transition: all 0.3s ease;
        }}
        
        .password-change-button:hover {{
            background-color: #f8f9fa;
            transform: translateY(-2px);
        }}
        
        .security-warning {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }}
        
        .security-warning .icon {{
            font-size: 24px;
            margin-bottom: 10px;
        }}
        
        .security-warning p {{
            color: #856404;
            font-weight: 500;
        }}
        
        .features {{
            margin: 30px 0;
        }}
        
        .features h3 {{
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }}
        
        .feature-list {{
            list-style: none;
            padding: 0;
        }}
        
        .feature-item {{
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            background-color: #f8f9fb;
            border-radius: 6px;
        }}
        
        .feature-icon {{
            background-color: #667eea;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 12px;
        }}
        
        .footer {{
            background-color: #343a40;
            color: #adb5bd;
            padding: 30px;
            text-align: center;
        }}
        
        .footer p {{
            margin-bottom: 10px;
        }}
        
        .footer a {{
            color: #667eea;
            text-decoration: none;
        }}
        
        @media (max-width: 600px) {{
            .email-container {{
                margin: 10px;
            }}
            
            .header, .content {{
                padding: 25px 20px;
            }}
            
            .detail-row {{
                flex-direction: column;
                gap: 5px;
            }}
            
            .detail-label {{
                min-width: auto;
            }}
        }}
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            {f'<img src="{settings.COMPANY_LOGO_URL}" alt="{settings.COMPANY_NAME}" class="logo">' if settings.COMPANY_LOGO_URL else ''}
            <h1>Welcome to {settings.COMPANY_NAME}</h1>
            <p>Your admin account has been successfully created</p>
        </div>
        
        <div class="content">
            <div class="welcome-message">
                <h2>Hello {admin_data.get('firstName', '')} {admin_data.get('lastName', '')}! 👋</h2>
                <p>We're excited to have you join our administrative team. Your account has been set up and you're ready to get started.</p>
            </div>
            
            <div class="admin-details">
                <div class="detail-row">
                    <span class="detail-label">Username:</span>
                    <span class="detail-value">{admin_data.get('username', '')}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value">{admin_data.get('email', '')}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Role:</span>
                    <span class="detail-value">{', '.join(admin_data.get('roles', ['Administrator']))}</span>
                </div>
            </div>
            
            <div class="security-warning">
                <div class="icon">🔐</div>
                <p><strong>Important:</strong> For security reasons, you need to set your own password. Please click the button below to set your password.</p>
            </div>
            
            <div class="password-change-section">
                <h3>Set Your Password</h3>
                <p>Click the button below to set your password and complete your account setup</p>
                <a href="{password_change_url}" class="password-change-button">Set Password</a>
                <p style="margin-top: 15px; font-size: 12px; opacity: 0.8;">This link is valid for 24 hours</p>
            </div>
            
        </div>
        
        <div class="footer">
            <p><strong>{settings.COMPANY_NAME}</strong></p>
            <p>If you have any questions, please contact your system administrator.</p>
            <p>This is an automated message, please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
"""
    return template

def send_admin_welcome_email_with_password_change(admin_data: Dict[str, Any]) -> bool:
    """
    Send welcome email to newly created admin with password change link
    """
    try:
        # Generate email content
        html_content = get_admin_welcome_email_with_password_change_template(admin_data)
        
        # Prepare email
        email_data = {
            'Source': f"{settings.EMAIL_FROM_NAME} <{settings.EMAIL_FROM_ADDRESS}>",
            'Destination': {
                'ToAddresses': [admin_data['email']]
            },
            'Message': {
                'Subject': {
                    'Data': f"Welcome to {settings.COMPANY_NAME} - Set Your Password",
                    'Charset': 'UTF-8'
                },
                'Body': {
                    'Html': {
                        'Data': html_content,
                        'Charset': 'UTF-8'
                    },
                    'Text': {
                        'Data': f"""
                            Welcome to {settings.COMPANY_NAME}!

                            Hello {admin_data.get('firstName', '')} {admin_data.get('lastName', '')},

                            Your admin account has been successfully created with the following details:

                            Username: {admin_data.get('username', '')}
                            Email: {admin_data.get('email', '')}
                            Role: {', '.join(admin_data.get('roles', ['Administrator']))}
                            Status: Active

                            For security reasons, you need to set your own password. Please click the link below to set your password:

                            {settings.APP_BASE_URL}/change-password?accessToken={admin_data.get('accessToken', '')}

                            This link is valid for 24 hours.

                            If you have any questions, please contact your system administrator.

                            Best regards,
                            {settings.COMPANY_NAME} Team
                                                    """,
                                                    'Charset': 'UTF-8'
                    }
                }
            }
        }
        
        # Send email via SES
        response = ses_client.send_email(**email_data)
        
        logger.info(f"Welcome email with password change link sent successfully to {admin_data['email']}. Message ID: {response['MessageId']}")
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_message = e.response['Error']['Message']
        logger.error(f"Failed to send welcome email to {admin_data['email']}: {error_code} - {error_message}")
        return False
        
    except Exception as e:
        logger.error(f"Unexpected error sending welcome email to {admin_data['email']}: {str(e)}")
        return False

def verify_ses_configuration() -> Dict[str, Any]:
    """
    Verify SES configuration and email sending capability
    """
    try:
        # Check if sender email is verified
        response = ses_client.get_identity_verification_attributes(
            Identities=[settings.EMAIL_FROM_ADDRESS]
        )
        
        verification_status = response.get('VerificationAttributes', {}).get(
            settings.EMAIL_FROM_ADDRESS, {}
        ).get('VerificationStatus', 'NotStarted')
        
        return {
            'configured': True,
            'sender_verified': verification_status == 'Success',
            'verification_status': verification_status,
            'sender_email': settings.EMAIL_FROM_ADDRESS
        }
        
    except Exception as e:
        logger.error(f"SES configuration check failed: {str(e)}")
        return {
            'configured': False,
            'error': str(e)
        } 