from sqlalchemy.orm import Session
from schemas.member_verification import MemberVerificationUpsert
from services import member_verification_api
from uuid import UUID

class MemberVerificationController:
    """
    Controller for member verification operations
    """

    def get_total_verified_members(self, db: Session):
        """
        Get total count of verified members
        """
        return member_verification_api.get_total_verified_members(db)
    
    def get_member_verification(self, member_uuid: UUID, db: Session):
        """
        Get verification information for a specific member
        """
        return member_verification_api.get_member_verification(member_uuid, db)
    
    def get_all_member_verifications(self, db: Session, page: int = 1, page_size: int = 10):
        """
        Get all member verifications with pagination
        """
        return member_verification_api.get_all_member_verifications(db, page, page_size)
    
    def upsert_member_verification(self, verification_data: MemberVerificationUpsert, admin_user_payload: dict, db: Session):
        """
        Create or update member verification
        """
        return member_verification_api.upsert_member_verification(verification_data, admin_user_payload, db)