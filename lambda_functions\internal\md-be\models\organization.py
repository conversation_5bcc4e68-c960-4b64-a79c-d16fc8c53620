from sqlalchemy import Column, String, DateTime, Integer, Text, Boolean, JSON, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
import uuid
from db.db import Base

class CoOrganization(Base):
    __tablename__ = "co_organizations"
    
    id = Column("id", Integer, primary_key=True, autoincrement=True)
    uuid = Column("uuid", UUID(as_uuid=True), default=uuid.uuid4, unique=True, nullable=False)
    name = Column("name", String(255), nullable=True)
    address1 = Column("address1", String(255), nullable=True)
    address2 = Column("address2", String(255), nullable=True)
    city = Column("city", String(255), nullable=True)
    state = Column("state", String(255), nullable=True)
    zip = Column("zip", String(255), nullable=True)
    phone = Column("phone", String(255), nullable=True)
    annualRevenue = Column("annualRevenue", String(255), nullable=True)
    industry = Column("industry", String(255), nullable=True)
    yearFounded = Column("yearFounded", String(255), nullable=True)
    businessProfileElementId = Column("businessProfileElementId", Integer, nullable=True)
    email = Column("email", String(255), nullable=True)
    companySize = Column("companySize", String(255), nullable=True)
    createdBy = Column("createdBy", UUID(as_uuid=True), nullable=True)
    updatedBy = Column("updatedBy", UUID(as_uuid=True), nullable=True)
    dateCreated = Column("dateCreated", DateTime, default=datetime.utcnow, nullable=False)
    dateUpdated = Column("dateUpdated", DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

class CoOrganizationVerifiedData(Base):
    __tablename__ = "co_organizations_verified_data"
    
    # Use uuid as primary key since id column doesn't exist in actual table
    uuid = Column("uuid", UUID(as_uuid=True), default=uuid.uuid4, primary_key=True, nullable=False)
    organizationId = Column("organizationId", Integer, ForeignKey("co_organizations.id"), nullable=False)
    name = Column("name", String(255), nullable=True)
    address = Column("address", String(255), nullable=True)
    city = Column("city", String(255), nullable=True)
    state = Column("state", String(255), nullable=True)
    zip = Column("zip", String(255), nullable=True)
    phone = Column("phone", String(255), nullable=True)
    ein = Column("ein", String(255), nullable=True)
    industry = Column("industry", String(255), nullable=True)
    foundingYear = Column("foundingYear", Integer, nullable=True)
    verificationStatus = Column("verificationStatus", String(255), nullable=False)  # Changed from Enum to String to match actual schema
    verificationType = Column("verificationType", String(255), nullable=False)      # Changed from Enum to String to match actual schema
    createdBy = Column("createdBy", UUID(as_uuid=True), nullable=True)             # Changed from String to UUID
    updatedBy = Column("updatedBy", UUID(as_uuid=True), nullable=True)             # Changed from String to UUID
    dateCreated = Column("dateCreated", DateTime, default=datetime.utcnow, nullable=False)
    dateUpdated = Column("dateUpdated", DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

class CoRelationsMembersOrganizations(Base):
    __tablename__ = "co_relations_members_organizations"
    memberId = Column("memberId", Integer, ForeignKey("co_members.id"), primary_key=True, nullable=False)
    organizationId = Column("organizationId", Integer, ForeignKey("co_organizations.id"), primary_key=True, nullable=False)
    createdBy = Column("createdBy", UUID(as_uuid=True), nullable=True)             # Exists in actual schema
    dateCreated = Column("dateCreated", DateTime, default=datetime.utcnow, nullable=False)

class CoMembersAwards(Base):
    __tablename__ = "co_members_awards"
    id = Column("id", Integer, primary_key=True, autoincrement=True)
    uuid = Column("uuid", UUID(as_uuid=True), default=uuid.uuid4, unique=True, nullable=False)
    memberId = Column("memberId", Integer, ForeignKey("co_members.id"), nullable=False)
    organizationId = Column("organizationId", Integer, ForeignKey("co_organizations.id"), nullable=False)
    awardListingElementId = Column("awardListingElementId", Integer, nullable=False)
    openWaterUserId = Column("openWaterUserId", Integer, nullable=True)
    openWaterApplicationId = Column("openWaterApplicationId", Integer, nullable=True)
    status = Column("status", String(255), nullable=False)
    progress = Column("progress", Integer, nullable=True)
    categories = Column("categories", JSON, nullable=True)
    isDisqualified = Column("isDisqualified", Boolean, default=False, nullable=True)
    isPreviousWinner = Column("isPreviousWinner", Boolean, default=False, nullable=True)
    isQualified = Column("isQualified", Boolean, default=False, nullable=True)
    isJudged = Column("isJudged", Boolean, nullable=True)
    isPaid = Column("isPaid", Boolean, nullable=True)
    isWinner = Column("isWinner", Boolean, nullable=True)
    winnerTypes = Column("winnerTypes", Text, nullable=True)
    applicationLink = Column("applicationLink", String(255), nullable=True)
    startedDate = Column("startedDate", DateTime, nullable=True)
    submittedDate = Column("submittedDate", DateTime, nullable=True)
    createdBy = Column("createdBy", String(255), nullable=True)             # Keep as String to match actual schema
    updatedBy = Column("updatedBy", String(255), nullable=True)             # Keep as String to match actual schema
    dateCreated = Column("dateCreated", DateTime, default=datetime.utcnow, nullable=False)
    dateUpdated = Column("dateUpdated", DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
