from sqlalchemy.orm import Session
from services.feature_api import *
from schemas.feature import *
from typing import List, Dict, Any
from fastapi import HTTPException

class FeatureController:
    """Controller for feature flags and bookmarks operations"""
    
    # ========================
    # Feature Flag Controllers
    # ========================
    
    def create_feature_flag(self, feature_flag: FeatureFlagCreate, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Create a new feature flag"""
        return create_feature_flag(feature_flag, user, db)
    
    def get_all_feature_flags(self, db: Session, perPage: int = 1, pageSize: int = 10) -> Dict[str, Any]:
        """Get all feature flags with pagination"""
        return get_all_feature_flags(db, perPage, pageSize)
    
    def get_feature_flag_by_uuid(self, feature_flag_uuid: str, db: Session) -> Dict[str, Any]:
        """Get feature flag by UUID"""
        return get_feature_flag_by_uuid(feature_flag_uuid, db)
    
    def update_feature_flag_by_uuid(self, feature_flag_uuid: str, feature_flag: FeatureFlagUpdate, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Update feature flag by UUID"""
        return update_feature_flag_by_uuid(feature_flag_uuid, feature_flag, user, db)
    
    def delete_feature_flag_by_uuid(self, feature_flag_uuid: str, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Delete feature flag by UUID"""
        return delete_feature_flag_by_uuid(feature_flag_uuid, user, db)
    
    def get_feature_flags_by_member(self, member_id: int, db: Session) -> Dict[str, Any]:
        """Get all feature flags for a specific member"""
        return get_feature_flags_by_member(member_id, db)
    
    def search_feature_flags(self, query: str, db: Session, perPage: int = 1, pageSize: int = 10) -> Dict[str, Any]:
        """Search feature flags by feature handle or member ID"""
        return search_feature_flags(query, db, perPage, pageSize)
    
    def bulk_create_feature_flags(self, feature_flags: List[FeatureFlagCreate], user: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Bulk create feature flags"""
        return bulk_create_feature_flags(feature_flags, user, db)
    
    # ========================
    # Bookmark Controllers
    # ========================
    
    def create_bookmark(self, bookmark: BookmarkCreate, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Create a new bookmark"""
        return create_bookmark(bookmark, user, db)
    
    def get_all_bookmarks(self, db: Session, perPage: int = 1, pageSize: int = 10) -> Dict[str, Any]:
        """Get all bookmarks with pagination"""
        return get_all_bookmarks(db, perPage, pageSize)
    
    def get_bookmark_by_uuid(self, bookmark_uuid: str, db: Session) -> Dict[str, Any]:
        """Get bookmark by UUID"""
        return get_bookmark_by_uuid(bookmark_uuid, db)
    
    def update_bookmark_by_uuid(self, bookmark_uuid: str, bookmark: BookmarkUpdate, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Update bookmark by UUID"""
        return update_bookmark_by_uuid(bookmark_uuid, bookmark, user, db)
    
    def delete_bookmark_by_uuid(self, bookmark_uuid: str, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Delete bookmark by UUID"""
        return delete_bookmark_by_uuid(bookmark_uuid, user, db)
    
    def get_bookmarks_by_member(self, member_id: int, db: Session) -> Dict[str, Any]:
        """Get all bookmarks for a specific member"""
        return get_bookmarks_by_member(member_id, db)
    
    def search_bookmarks(self, query: str, db: Session, perPage: int = 1, pageSize: int = 10) -> Dict[str, Any]:
        """Search bookmarks by member ID or entry ID"""
        return search_bookmarks(query, db, perPage, pageSize)
    
    def bulk_create_bookmarks(self, bookmarks: List[BookmarkCreate], user: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """Bulk create bookmarks"""
        return bulk_create_bookmarks(bookmarks, user, db) 