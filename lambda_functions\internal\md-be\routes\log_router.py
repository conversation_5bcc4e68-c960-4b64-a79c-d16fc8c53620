from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from db.db import get_db
from controller.log_controller import (
    list_logs,
    export_logs_to_csv,
    get_log_actions,
    get_log_export_fields
)
from schemas.log import LogFilters, LogExportRequest
from dependencies.admin_jwt import validate_jwt_token
from dependencies.member_jwt import validate_token as member_validate_jwt_token
from typing import Optional, Union, List
from datetime import datetime
from uuid import UUID
from utils.cloudwatch_logger import cloudwatch_logger

router = APIRouter(prefix="/logs", tags=["logs"])

@router.get("/")
async def get_logs(
    start_timestamp: Optional[datetime] = Query(None, description="Start timestamp for filtering"),
    end_timestamp: Optional[datetime] = Query(None, description="End timestamp for filtering"),
    purpose: Optional[str] = Query(None, description="Search text in purpose field"),
    action: Optional[str] = Query(None, description="Filter by action type"),
    action_exclude: Optional[List[str]] = Query(None, description="Exclude logs matching any of these action types"),
    sort_by: Optional[str] = Query("timestamp", description="Field to sort by (timestamp, username, action, purpose)"),
    sort_order: Optional[str] = Query("desc", description="Sort order: asc or desc"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of items per page"),
    db: Session = Depends(get_db),
    current_user = Depends(validate_jwt_token)  # Only admin users can view logs
):
    """
    Get logs with optional filtering, sorting, and pagination
    
    **Filters:**
    - `start_timestamp`: Filter logs from this timestamp onwards
    - `end_timestamp`: Filter logs until this timestamp
    - `purpose`: Search for text within the purpose field (case-insensitive)
    - `action`: Filter by specific action type (exact match)
    - `action_exclude`: Exclude logs whose action matches any provided values. Supports either multiple params (e.g. `action_exclude=a&action_exclude=b`) or comma-separated (e.g. `action_exclude=a,b`).
    
    **Sorting:**
    - `sort_by`: Field to sort by (timestamp, username, action, purpose)
    - `sort_order`: Sort order (asc or desc)
    
    **Pagination:**
    - `page`: Page number (starts from 1)
    - `page_size`: Number of items per page (1-1000)
    
    **Response includes:**
    - `logs`: List of log entries with extracted context fields
    - `total_count`: Total number of logs matching the filters
    - `page`: Current page number
    - `page_size`: Items per page
    """
    # Normalize action_exclude to support comma-separated values
    normalized_action_exclude: Optional[List[str]] = action_exclude
    if action_exclude and len(action_exclude) == 1 and "," in action_exclude[0]:
        normalized_action_exclude = [v for v in action_exclude[0].split(",") if v]

    filters = LogFilters(
        start_timestamp=start_timestamp,
        end_timestamp=end_timestamp,
        purpose=purpose,
        action=action,
        action_exclude=normalized_action_exclude,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    return list_logs(filters, page, page_size, db)

@router.post("/export")
async def export_logs(
    request: LogExportRequest,
    db: Session = Depends(get_db),
    current_user = Depends(validate_jwt_token)  # Only admin users can export logs
):
    """
    Export logs to CSV based on filters and selected fields
    
    **Request Body:**
    - `filters`: Same filtering options as GET /logs endpoint
    - `selected_fields`: Array of field names to include in CSV
    - `notes`: Optional description of export purpose
    
    **Available Fields:**
    - `timestamp`: When the log entry was created
    - `username`: Username of the admin who performed the action
    - `admin_uuid`: UUID of the admin who performed the action
    - `action`: Type of action performed
    - `filters_applied`: Filters applied from context JSON
    - `selected_fields`: Selected fields from context JSON
    - `purpose`: Purpose or description of the action
    
    **Response:**
    - `csv_content`: Base64 encoded CSV content
    - `filename`: Suggested filename for download
    - `export_timestamp`: When the export was generated
    
    **Note:** This action itself will be logged in the system
    """
    # Get user UUID from current user (admin or member)
    user_uuid = current_user.get("sub") 
    if not user_uuid:
        raise HTTPException(status_code=401, detail="User UUID not found in token")
    
    if isinstance(user_uuid, str):
        user_uuid = UUID(user_uuid)
    
    return export_logs_to_csv(request, user_uuid, db)

@router.get("/actions")
async def get_available_actions(
    db: Session = Depends(get_db),
    current_user = Depends(validate_jwt_token)  # Only admin users can view available actions
):
    """
    Get list of unique action types available in the log table
    
    **Response:**
    - `actions`: Array of unique action strings found in the database
    
    This endpoint is useful for populating filter dropdowns in the frontend.
    """
    return get_log_actions(db)

@router.get("/export-fields")
async def get_export_fields(
    current_user = Depends(validate_jwt_token)  # Only admin users can view export fields
):
    """
    Get available fields for CSV export
    
    **Response:**
    - `fields`: Array of field objects with:
    - `field`: Field name for API requests
    - `display_name`: Human-readable field name
    - `description`: Description of what the field contains
    
    This endpoint is useful for building dynamic export field selection interfaces.
    """
    return get_log_export_fields()

# Alternative endpoint that allows both admin and member access (if needed)
@router.get("/my-logs")
async def get_my_logs(
    start_timestamp: Optional[datetime] = Query(None, description="Start timestamp for filtering"),
    end_timestamp: Optional[datetime] = Query(None, description="End timestamp for filtering"),
    purpose: Optional[str] = Query(None, description="Search text in purpose field"),
    action: Optional[str] = Query(None, description="Filter by action type"),
    sort_by: Optional[str] = Query("timestamp", description="Field to sort by (timestamp, username, action, purpose)"),
    sort_order: Optional[str] = Query("desc", description="Sort order: asc or desc"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of items per page"),
    db: Session = Depends(get_db),
    current_user: Union[dict, None] = Depends(lambda: None)  # Try both auth methods
):
    """
    Get logs for the current user (members can only see their own logs)
    
    This endpoint automatically filters logs by the current user's UUID.
    Members can only see their own activity logs.
    Admins can see all logs through the main /logs endpoint.
    
    **Filters:**
    - `start_timestamp`: Filter logs from this timestamp onwards
    - `end_timestamp`: Filter logs until this timestamp
    - `purpose`: Search for text within the purpose field (case-insensitive)
    - `action`: Filter by specific action type (exact match)
    
    **Sorting:**
    - `sort_by`: Field to sort by (timestamp, username, action, purpose)
    - `sort_order`: Sort order (asc or desc)
    
    **Pagination:**
    - `page`: Page number (starts from 1)
    - `page_size`: Number of items per page (1-1000)
    """
    # Try to get user from either admin or member auth
    try:
        if not current_user:
            try:
                current_user = validate_jwt_token()
            except:
                current_user = member_validate_jwt_token()
    except:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    # Get user UUID from current user
    user_uuid = current_user.get("uuid") or current_user.get("userUuid")
    if not user_uuid:
        raise HTTPException(status_code=401, detail="User UUID not found in token")
    
    # Create filters with additional user UUID filter
    filters = LogFilters(
        start_timestamp=start_timestamp,
        end_timestamp=end_timestamp,
        purpose=purpose,
        action=action,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    # Note: We would need to modify the service to accept userUuid filter
    # For now, this is a placeholder endpoint structure
    return list_logs(filters, page, page_size, db)

@router.get("/print-logs/search")
async def search_print_logs_by_id(
    resource_id: str = Query(..., description="Resource ID to search for"),
    resource_type: Optional[str] = Query(None, description="Optional resource type filter (e.g., 'member', 'organization', 'admin')"),
    start_time: Optional[datetime] = Query(None, description="Start time for search (default: 24 hours ago)"),
    end_time: Optional[datetime] = Query(None, description="End time for search (default: now)"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of log entries to return"),
    current_user = Depends(validate_jwt_token)  # Only admin users can search print logs
):
    """
    Search in-memory print logs by resource ID for debugging purposes
    
    This endpoint allows searching in-memory print logs for specific resource IDs to help debug issues.
    Only admin users can access this endpoint.
    
    **Parameters:**
    - `resource_id`: The resource ID to search for (required)
    - `resource_type`: Optional resource type filter to narrow down results
    - `start_time`: Start time for search (default: 24 hours ago)
    - `end_time`: End time for search (default: now)
    - `limit`: Maximum number of log entries to return (1-1000)
    
    **Response:**
    - `log_entries`: Array of log entries with timestamp, log stream name, and data
    - `total_count`: Total number of log entries found
    - `search_criteria`: The search criteria used
    
    **Note:** This searches in-memory print logs, not the database logs. Use this for detailed debugging.
    """
    try:
        log_entries = cloudwatch_logger.search_logs_by_id(
            resource_id=resource_id,
            resource_type=resource_type,
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )
        
        return {
            "message": f"Found {len(log_entries)} log entries for resource ID: {resource_id}",
            "data": {
                "log_entries": log_entries,
                "total_count": len(log_entries),
                "search_criteria": {
                    "resource_id": resource_id,
                    "resource_type": resource_type,
                    "start_time": start_time.isoformat() if start_time else None,
                    "end_time": end_time.isoformat() if end_time else None,
                    "limit": limit
                }
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to search print logs: {str(e)}"
        ) 