from passlib.context import Crypt<PERSON>ontext
from fastapi import HTTPException, status
import re

# Use bcrypt as the hashing algorithm
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def enforce_password_policy(password: str):
    if len(password) < 8:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Password must be at least 8 characters long.",
                "status_code": status.HTTP_400_BAD_REQUEST
            }
        )
    if not re.search(r"[A-Z]", password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Password must contain at least one uppercase letter.",
                "status_code": status.HTTP_400_BAD_REQUEST
            }
        )
    if not re.search(r"[a-z]", password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Password must contain at least one lowercase letter.",
                "status_code": status.HTTP_400_BAD_REQUEST
            }
        )
    if not re.search(r"[0-9]", password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Password must contain at least one digit.",
                "status_code": status.HTTP_400_BAD_REQUEST
            }
        )
    if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Password must contain at least one special character.",
                "status_code": status.HTTP_400_BAD_REQUEST
            }
        )

def hash_password(password: str) -> str:
    enforce_password_policy(password)
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
