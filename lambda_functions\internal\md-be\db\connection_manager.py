# Database Connection Manager with AWS Secrets Manager authentication

import logging
from sqlalchemy import create_engine, text, Engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from typing import Optional
from urllib.parse import quote_plus
from .secrets_manager_auth import get_secrets_auth, initialize_secrets_auth

# Configure logging
logger = logging.getLogger(__name__)

class DatabaseConnectionManager:
    # Manages database connections using AWS Secrets Manager authentication

    def __init__(self, secret_name: str, region: str):
        self.secret_name = secret_name
        self.region = region

        # Initialize secrets manager auth
        self.secrets_auth = initialize_secrets_auth(secret_name, region)

        # SQLAlchemy components
        self._engine: Optional[Engine] = None
        self._session_local: Optional[sessionmaker] = None

        logger.info(f"Database Connection Manager initialized with Secrets Manager secret '{secret_name}'")
    
    def _build_connection_url(self) -> str:
        # Build PostgreSQL connection URL with all config from Secrets Manager
        try:
            # Get all database configuration from Secrets Manager
            credentials = self.secrets_auth.get_database_credentials()
            username = credentials['username']
            password = credentials['password']
            host = credentials['host']
            port = credentials['port']
            dbname = credentials['dbname']

            # URL encode the password to handle special characters
            encoded_password = quote_plus(password)

            # Build PostgreSQL connection URL
            connection_url = (
                f"postgresql://{username}:{encoded_password}@"
                f"{host}:{port}/{dbname}"
            )

            connection_logs = [
                f"🔗 Building connection URL for {username}@{host}:{port}/{dbname}",
                f"Full connection URL (without password): postgresql://{username}:***@{host}:{port}/{dbname}"
            ]
            
            for log in connection_logs:
                logger.info(log)
            
            # Store logs for response
            self._connection_logs = connection_logs
            
            return connection_url

        except Exception as e:
            logger.error(f"Failed to build connection URL: {str(e)}")
            raise
    
    def _create_engine(self) -> Engine:
        # Create SQLAlchemy engine with Secrets Manager authentication
        try:
            connection_url = self._build_connection_url()

            # Create engine with appropriate settings for RDS
            engine = create_engine(
                connection_url,
                # Connection pool settings
                pool_size=5,
                max_overflow=10,
                pool_timeout=30,
                pool_recycle=1800,  # Recycle connections every 30 minutes (shorter for password-based auth)
                pool_pre_ping=True,  # Validate connections before use
                # SSL settings for RDS
                connect_args={
                    "sslmode": "require",
                    "connect_timeout": 10
                },
                # Logging
                echo=False  # Set to True for SQL query logging
            )
            
            engine_logs = [
                "✅ SQLAlchemy engine created successfully",
                "   Pool size: 5, Max overflow: 10, Pool timeout: 30s",
                "   SSL mode: require, Connect timeout: 10s"
            ]
            
            for log in engine_logs:
                logger.info(log)
            
            # Store logs for response
            self._engine_logs = engine_logs
            
            return engine
            
        except Exception as e:
            logger.error(f"Failed to create SQLAlchemy engine: {str(e)}")
            raise
    
    def get_engine(self) -> Engine:
        # Get or create SQLAlchemy engine
        if self._engine is None:
            self._engine = self._create_engine()
        return self._engine
    
    def get_session_local(self) -> sessionmaker:
        # Get or create SQLAlchemy SessionLocal
        if self._session_local is None:
            engine = self.get_engine()
            self._session_local = sessionmaker(bind=engine, autoflush=False, autocommit=False)
            logger.info("SQLAlchemy SessionLocal created successfully")
        return self._session_local
    
    def test_connection(self) -> dict:
        # Test the database connection
        try:
            engine = self.get_engine()

            # Get connection info from secret
            credentials = self.secrets_auth.get_database_credentials()

            # Test connection with a simple query
            with engine.connect() as connection:
                result = connection.execute(text("SELECT 1 as test"))
                test_value = result.scalar()

                # Get database info
                db_result = connection.execute(text("SELECT current_database(), current_user, version()"))
                db_info = db_result.fetchone()

            logger.info("Database connection test successful")

            return {
                "status": "success",
                "test_query_result": test_value,
                "database": db_info[0] if db_info else None,
                "user": db_info[1] if db_info else None,
                "version": db_info[2] if db_info else None,
                "hostname": credentials['host'],
                "port": credentials['port'],
                "region": self.region,
                "secret_info": self.secrets_auth.get_secret_info()
            }
            
        except SQLAlchemyError as e:
            logger.error(f"Database connection test failed (SQLAlchemy): {str(e)}")
            try:
                credentials = self.secrets_auth.get_database_credentials()
                hostname = credentials['host']
                port = credentials['port']
            except:
                hostname = "unknown"
                port = "unknown"
            return {
                "status": "error",
                "error_type": "database_error",
                "error_message": str(e),
                "hostname": hostname,
                "port": port,
                "region": self.region
            }
        except Exception as e:
            logger.error(f"Database connection test failed: {str(e)}")
            try:
                credentials = self.secrets_auth.get_database_credentials()
                hostname = credentials['host']
                port = credentials['port']
            except:
                hostname = "unknown"
                port = "unknown"
            return {
                "status": "error",
                "error_type": "connection_error",
                "error_message": str(e),
                "hostname": hostname,
                "port": port,
                "region": self.region
            }
    
    def refresh_connection(self) -> dict:
        # Force refresh the database connection by recreating the engine
        try:
            logger.info("Refreshing database connection")

            # Dispose of current engine to force recreation with fresh password
            if self._engine is not None:
                self._engine.dispose()
                self._engine = None
                self._session_local = None
                logger.info("Disposed old engine and session factory")

            # Test the new connection (this will create a new engine)
            test_result = self.test_connection()

            if test_result["status"] == "success":
                logger.info("Database connection refreshed successfully")
                return {
                    "status": "success",
                    "message": "Database connection refreshed successfully",
                    "test_result": test_result
                }
            else:
                logger.error("Database connection refresh failed during test")
                return {
                    "status": "error",
                    "message": "Connection refresh failed during test",
                    "test_result": test_result
                }

        except Exception as e:
            logger.error(f"Failed to refresh database connection: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to refresh connection: {str(e)}",
                "error_type": "refresh_error"
            }
    
    def get_logs(self) -> dict:
        # Get all logs for response
        logs = {
            "connection_url": getattr(self, '_connection_logs', ['No connection URL logs']),
            "engine_creation": getattr(self, '_engine_logs', ['No engine creation logs']),
            "secrets_auth_logs": self.secrets_auth.get_logs()
        }
        return logs

    def get_connection_info(self) -> dict:
        # Get information about the current connection configuration
        try:
            credentials = self.secrets_auth.get_database_credentials()
            return {
                "hostname": credentials['host'],
                "port": credentials['port'],
                "username": credentials['username'],
                "database": credentials['dbname'],
                "region": self.region,
                "secret_info": self.secrets_auth.get_secret_info(),
                "engine_created": self._engine is not None,
                "session_factory_created": self._session_local is not None
            }
        except Exception as e:
            return {
                "hostname": "unknown",
                "port": "unknown",
                "username": "unknown",
                "database": "unknown",
                "region": self.region,
                "secret_info": self.secrets_auth.get_secret_info(),
                "engine_created": self._engine is not None,
                "session_factory_created": self._session_local is not None,
                "error": f"Failed to get connection info: {str(e)}"
            }


def initialize_connection_manager(secret_name: str, region: str) -> DatabaseConnectionManager:
    # Create a new database connection manager instance
    connection_manager = DatabaseConnectionManager(secret_name, region)
    logger.info("Database Connection Manager created")
    return connection_manager
