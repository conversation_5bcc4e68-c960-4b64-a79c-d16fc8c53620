from schemas.rbac import RoleCreate
from schemas.rbac import *
from services import rbac_api
from sqlalchemy.orm import Session
from typing import List

class RBACController:
    # -------------------------
    # Role Controllers
    # -------------------------
    
    def create_role(self, role: RoleCreate, user, db: Session):
        return rbac_api.create_role(role, user, db)
    
    def get_all_roles(self, db: Session, perPage: int, pageSize: int) -> dict:
        return rbac_api.get_all_roles(db, perPage=perPage, pageSize=pageSize)

    def get_role_by_uuid(self, role_uuid: str, db: Session) -> dict:
        return rbac_api.get_role_by_uuid(role_uuid, db)

    def update_role(self, role_uuid: str, role_update: RoleUpdate, user, db: Session) -> dict:
        return rbac_api.update_role(role_uuid, role_update, user, db)
    
    def delete_role(self, role_uuid: str, db: Session):
        return rbac_api.delete_role(role_uuid, db)
    
    # -------------------------
    # Module Controllers
    # -------------------------
    
    def create_module(self, module: ModuleCreate, user, db: Session):
        return rbac_api.create_module(module, user, db)
    
    def get_all_modules(self, db: Session) -> dict:
        return rbac_api.get_all_modules(db)
    
    def get_module_by_id(self, uuid: str, db: Session) -> dict:
        return rbac_api.get_module_by_id(uuid, db)
    
    def update_module(self, uuid: str, module_update: ModuleUpdate, user, db: Session) -> dict:
        return rbac_api.update_module(uuid, module_update, user, db)
    
    def delete_module(self, uuid: str, db: Session):
        return rbac_api.delete_module(uuid, db)
    
    # -------------------------
    # Role Module Permission Controllers
    # -------------------------
    
    def create_role_module_permission(self, permission: RoleModulePermissionCreate, user, db: Session):
        return rbac_api.create_role_module_permission(permission, user, db)
    
    def get_all_role_module_permissions(self, db: Session) -> dict:
        return rbac_api.get_all_role_module_permissions(db)

    def get_role_module_permission_by_id(self, permission_id: int, db: Session) -> dict:
        return rbac_api.get_role_module_permission_by_id(permission_id, db)

    def get_permissions_by_roleid(self, roleid: int, db: Session) -> dict:
        return rbac_api.get_permissions_by_roleid(roleid, db)
    
    def update_role_module_permission(self, permission_id: int, permission_update: RoleModulePermissionUpdate, user, db: Session) -> dict:
        return rbac_api.update_role_module_permission(permission_id, permission_update, user, db)

    def delete_role_module_permission(self, role_uuid: str, module_uuid: str, db: Session) -> dict:
        return rbac_api.delete_role_module_permission(role_uuid, module_uuid, db)
    
    # -------------------------
    # Advanced RBAC Controllers
    # -------------------------
    
    def upsert_role_with_permissions(self, request_data: dict, user, db: Session):
        return rbac_api.upsert_role_with_permissions(request_data, user, db)
