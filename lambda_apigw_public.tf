#create API gateway
resource "aws_apigatewayv2_api" "lambda-md-be-apigw" {
  name          = "${var.function_name}-gateway"
  protocol_type = "HTTP"
}

resource "aws_apigatewayv2_integration" "lambda-md-be-apigw-integration" {
  api_id                 = aws_apigatewayv2_api.lambda-md-be-apigw.id
  integration_type       = "AWS_PROXY"
  integration_uri        = module.lambda-md-be.lambda_function_invoke_arn
  integration_method     = "POST"
  payload_format_version = "2.0"
}

resource "aws_apigatewayv2_route" "lambda-md-be-apigw-route" {
  api_id    = aws_apigatewayv2_api.lambda-md-be-apigw.id
  route_key = "$default"
  target    = "integrations/${aws_apigatewayv2_integration.lambda-md-be-apigw-integration.id}"
}

resource "aws_cloudwatch_log_group" "lamba-md-be-apigw-loggroup" {
  name              = "/aws/apigateway/${var.function_name}-gateway"
  retention_in_days = 7
}

resource "aws_apigatewayv2_stage" "default" {
  api_id      = aws_apigatewayv2_api.lambda-md-be-apigw.id
  name        = "$default"
  auto_deploy = true

  default_route_settings {
    throttling_burst_limit = 5
    throttling_rate_limit  = 10
    data_trace_enabled     = false
  }

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.lamba-md-be-apigw-loggroup.arn
    format = jsonencode({
      requestId      = "$context.requestId",
      httpMethod     = "$context.httpMethod",
      routeKey       = "$context.routeKey",
      status         = "$context.status",
      integration    = "$context.integrationStatus",
      responseLength = "$context.responseLength"
    })
  }
}

module "lambda-md-be" {
  source          = "github.com/USChamber/terraform-aws-uschamber//modules/global/lambda"
  function_name   = var.function_name
  description     = var.description
  create_role     = true
  attach_network_policy = true
  attach_ecr_policy = true
  timeout         = var.timeout
  memory_size     = var.memory_size
  package_type    = var.package_type
  image_uri       = var.image_uri
  tags            = merge(module.label.tags, { Name = var.function_name })
  create_ecr_repo = true
  ecr_repo        = var.ecr_repo
  ecr_repo_tags   = merge(module.label.tags, { Name = var.ecr_repo })
  kms_key_arn     = aws_kms_key.lambda-md-be-kms.arn
  vpc_security_group_ids = [aws_security_group.lambda-md-be-sg.id]
  vpc_subnet_ids  = data.terraform_remote_state.vpc.outputs.stg_private_subnets
  create_current_version_allowed_triggers = false
  allowed_triggers = {
    APIGatewayAny = {
      service    = "apigateway"
      source_arn = "${aws_apigatewayv2_api.lambda-md-be-apigw.execution_arn}/*/*"
    }
  }
}
