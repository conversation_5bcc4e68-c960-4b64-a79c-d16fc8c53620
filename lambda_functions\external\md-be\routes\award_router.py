from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from controller.organization_controller import OrganizationController
from db.db import get_db
from dependencies.admin_jwt import validate_jwt_token
from schemas.organization import *
from typing import List, Optional

router = APIRouter()
organizationController = OrganizationController()

# ========================
# Award Endpoints
# ========================

@router.post("/awards", status_code=status.HTTP_201_CREATED, dependencies=[Depends(validate_jwt_token)])
async def create_member_award(
    award: MemberAwardCreate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Create a new member award"""
    return organizationController.create_member_award(award, user, db)

@router.get("/awards", dependencies=[Depends(validate_jwt_token)])
async def get_all_awards(
    memberid: Optional[int] = Query(None),
    organizationid: Optional[int] = Query(None),
    db: Session = Depends(get_db)
):
    """Get all awards with optional filtering"""
    return organizationController.get_member_awards(memberid, organizationid, db)

@router.get("/awards/{memberid}/{organizationid}/{awardlistingelementid}", dependencies=[Depends(validate_jwt_token)])
async def get_award_by_id(
    memberid: int,
    organizationid: int,
    awardlistingelementid: int,
    db: Session = Depends(get_db)
):
    """Get award by composite key"""
    return organizationController.get_member_award_by_id(memberid, organizationid, awardlistingelementid, db)

@router.put("/awards/{memberid}/{organizationid}/{awardlistingelementid}", dependencies=[Depends(validate_jwt_token)])
async def update_award(
    memberid: int,
    organizationid: int,
    awardlistingelementid: int,
    award: MemberAwardUpdate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Update award by composite key"""
    return organizationController.update_member_award(memberid, organizationid, awardlistingelementid, award, user, db)

@router.delete("/awards/{memberid}/{organizationid}/{awardlistingelementid}", dependencies=[Depends(validate_jwt_token)])
async def delete_award(
    memberid: int,
    organizationid: int,
    awardlistingelementid: int,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Delete award by composite key"""
    return organizationController.delete_member_award(memberid, organizationid, awardlistingelementid, user, db)

@router.get("/member/{member_id}/awards", dependencies=[Depends(validate_jwt_token)])
async def get_awards_by_member(member_id: int, db: Session = Depends(get_db)):
    """Get all awards for a specific member"""
    return organizationController.get_awards_by_member(member_id, db)

@router.get("/organization/{organization_id}/awards", dependencies=[Depends(validate_jwt_token)])
async def get_awards_by_organization(organization_id: int, db: Session = Depends(get_db)):
    """Get all awards for a specific organization"""
    return organizationController.get_awards_by_organization(organization_id, db)

# Note: Search and bulk operations for awards are not implemented in the current organization_api
# These endpoints would need to be added to the organization_api service layer 