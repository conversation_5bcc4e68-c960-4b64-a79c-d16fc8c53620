from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from uuid import uuid4
from schemas.rbac import *
from models import rbac
from typing import List
from models.admin import AdminModel
from utils.response_utils import (
    success_response, created_response, not_found_response,
    conflict_response, bad_request_response, internal_server_error_response
)

# -------------------------
# Role APIs
# -------------------------

def create_role(role: RoleCreate, user, db: Session):
    existing_role = db.query(rbac.Role).filter(rbac.Role.name == role.name).first()
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={
                "message": f"Role with name '{role.name}' already exists",
                "status_code": status.HTTP_409_CONFLICT
            }
        )
    
    # extract user id from the token
    user_id = user.get("sub")
    new_role = rbac.Role(
        name=role.name,
        slug=role.name.lower().replace(" ", "_"),
        description=role.description,  # Set description if provided
        createdBy=user_id,
        updatedBy=user_id
    )

    db.add(new_role)
    db.commit()
    db.refresh(new_role)
    
    return created_response(
        "Role created successfully",
        {"role": RoleResponse.model_validate(new_role)}
    )

def get_all_roles(db: Session, perPage: int, pageSize: int) -> dict:
    roles = db.query(rbac.Role).offset((perPage - 1) * pageSize).limit(pageSize).all()

    roles_list = []

    # send the response as a list of RoleResponseEnriched with createdBy as object
    for role in roles:
        # Create the base role response
        role_data = {
            "name": role.name,
            "slug": role.slug,
            "uuid": role.uuid,
            "description": role.description,
            "updatedBy": role.updatedBy,
            "dateCreated": role.dateCreated,
            "dateUpdated": role.dateUpdated
        }

        # Handle createdBy enrichment
        if role.createdBy is not None:
            creator = db.query(AdminModel).filter(AdminModel.cognitoId == str(role.createdBy)).first()
            role_data["createdBy"] = {
                "uuid": role.createdBy,
                "username": creator.username if creator else "unknown"
            }
        else:
            role_data["createdBy"] = {
                "uuid": None,
                "username": "unknown"
            }

        role_response = RoleResponseEnriched(**role_data)
        roles_list.append(role_response)

    return success_response(
        "Roles retrieved successfully",
        {"roles": roles_list}
    )

def get_role_by_uuid(role_uuid: str, db: Session) -> dict:
    role = db.query(rbac.Role).filter(rbac.Role.uuid == role_uuid).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Role with uuid '{role_uuid}' not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    return success_response(
        "Role retrieved successfully",
        {"role": RoleResponse.model_validate(role)}
    )

def update_role(role_uuid: str, role_update: RoleUpdate, user, db: Session) -> dict:
    role = db.query(rbac.Role).filter(rbac.Role.uuid == role_uuid).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Role with uuid '{role_uuid}' not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )

    # Check for name conflicts only if name is being changed
    if role_update.name and role_update.name != role.name:
        existing_role = db.query(rbac.Role).filter(rbac.Role.name == role_update.name).first()
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": f"Role with name '{role_update.name}' already exists",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )

    user_id = user.get("sub")

    if role_update.name:
        setattr(role, 'name', role_update.name)
        setattr(role, 'slug', role_update.name.lower().replace(" ", "_"))

    if role_update.description is not None:
        setattr(role, 'description', role_update.description)

    setattr(role, 'updatedBy', user_id)

    db.commit()
    db.refresh(role)

    return success_response(
        "Role updated successfully",
        {"role": RoleResponse.model_validate(role)}
    )

def delete_role(role_uuid: str, db: Session):
    role = db.query(rbac.Role).filter(rbac.Role.uuid == role_uuid).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Role with uuid '{role_uuid}' not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    
    db.delete(role)
    db.commit()
    
    return success_response(f"Role '{role.name}' deleted successfully")

# -------------------------
# Module APIs
# -------------------------

def create_module(module: ModuleCreate, user, db: Session):
    existing_module = db.query(rbac.Module).filter(rbac.Module.name == module.name).first()
    if existing_module:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={
                "message": f"Module with name '{module.name}' already exists",
                "status_code": status.HTTP_409_CONFLICT
            }
        )
    
    user_id = user.get("sub")
    new_module = rbac.Module(
        uuid=uuid4(),
        name=module.name,
        slug=module.name.lower().replace(" ", "_"),
        createdBy=user_id,
        updatedBy=user_id,
    )

    db.add(new_module)
    db.commit()
    db.refresh(new_module)
    
    return created_response(
        "Module created successfully",
        {"module": ModuleResponse.model_validate(new_module)}
    )

def get_all_modules(db: Session) -> dict:
    modules = db.query(rbac.Module).all()
    return success_response(
        "Modules retrieved successfully",
        {"modules": [ModuleResponse.model_validate(module) for module in modules]}
    )

def get_module_by_id(uuid: str, db: Session) -> dict:
    module = db.query(rbac.Module).filter(rbac.Module.uuid == uuid).first()
    if not module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Module {uuid} not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    return success_response(
        "Module retrieved successfully",
        {"module": ModuleResponse.model_validate(module)}
    )

def update_module(uuid: str, module_update: ModuleUpdate, user, db: Session) -> dict:
    module = db.query(rbac.Module).filter(rbac.Module.uuid == uuid).first()
    if not module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Module {uuid} not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    if module_update.name != module.name:
        existing_module = db.query(rbac.Module).filter(rbac.Module.name == module_update.name).first()
        if existing_module:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": f"Module with name '{module_update.name}' already exists",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )
    user_id = user.get("sub")
    setattr(module, 'name', module_update.name)
    setattr(module, 'slug', module_update.name.lower().replace(" ", "_"))
    setattr(module, 'updatedBy', user_id)
    db.commit()
    db.refresh(module)
    return success_response(
        "Module updated successfully",
        {"module": ModuleResponse.model_validate(module)}
    )

def delete_module(uuid: str, db: Session):
    module = db.query(rbac.Module).filter(rbac.Module.uuid == uuid).first()
    if not module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Module {uuid} not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    
    db.delete(module)
    db.commit()
    
    return success_response(f"Module '{module.name}' deleted successfully")

# -------------------------
# Role Module Permission APIs
# -------------------------

def create_role_module_permission(permission: RoleModulePermissionCreate, user, db: Session):
    # Check if role exists
    role = db.query(rbac.Role).filter(rbac.Role.id == permission.roleId).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Role with id {permission.roleId} not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    
    # Check if module exists
    module = db.query(rbac.Module).filter(rbac.Module.id == permission.moduleId).first()
    if not module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Module with id {permission.moduleId} not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    
    # Check if permission already exists
    existing_permission = db.query(rbac.RoleModulePermission).filter(
        rbac.RoleModulePermission.roleId == permission.roleId,
        rbac.RoleModulePermission.moduleId == permission.moduleId
    ).first()
    
    if existing_permission:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={
                "message": f"Permission already exists for role '{role.name}' and module '{module.name}'",
                "status_code": status.HTTP_409_CONFLICT
            }
        )
    
    user_id = user.get("sub")
    new_permission = rbac.RoleModulePermission(
        roleId=permission.roleId,
        moduleId=permission.moduleId,
        create=permission.create,
        update=permission.update,
        delete=permission.delete,
        view=permission.view,
        createdBy=user_id
    )

    db.add(new_permission)
    db.commit()
    db.refresh(new_permission)
    
    return created_response(
        "Role module permission created successfully",
        {"permission": RoleModulePermissionResponse.model_validate(new_permission)}
    )

def get_all_role_module_permissions(db: Session) -> dict:
    """Get all role module permissions grouped by role with role and module details"""
    
    # Query permissions with joins to get role and module details
    permissions_query = db.query(
        rbac.RoleModulePermission,
        rbac.Role.name.label('role_name'),
        rbac.Role.slug.label('role_slug'), 
        rbac.Role.description.label('role_description'),
        rbac.Module.name.label('module_name'),
        rbac.Module.slug.label('module_slug')
    ).join(
        rbac.Role, rbac.RoleModulePermission.roleId == rbac.Role.id
    ).join(
        rbac.Module, rbac.RoleModulePermission.moduleId == rbac.Module.id
    ).all()
    
    # Group permissions by role
    role_permissions_map = {}
    
    for permission, role_name, role_slug, role_description, module_name, module_slug in permissions_query:
        role_id = permission.roleId
        
        if role_id not in role_permissions_map:
            role_permissions_map[role_id] = {
                "roleName": role_name,
                "roleSlug": role_slug,
                "roleDescription": role_description,
                "modulePermissions": []
            }
        
        # Add module permission with details
        module_permission = {
            "moduleName": module_name,
            "moduleSlug": module_slug,
            "create": permission.create,
            "update": permission.update,
            "delete": permission.delete,
            "view": permission.view,
            "createdBy": str(permission.createdBy),
            "updatedBy": str(permission.updatedBy),
            "dateCreated": permission.dateCreated.isoformat(),
            "dateUpdated": permission.dateUpdated.isoformat()
        }
        
        role_permissions_map[role_id]["modulePermissions"].append(module_permission)
    
    return success_response(
        "Role module permissions retrieved successfully",
        {"permissions": list(role_permissions_map.values())}
    )

def get_role_module_permission_by_id(permission_id: int, db: Session) -> dict:
    permission = db.query(rbac.RoleModulePermission).filter(rbac.RoleModulePermission.id == permission_id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Permission with id {permission_id} not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    return success_response(
        "Role module permission retrieved successfully",
        {"permission": RoleModulePermissionResponse.model_validate(permission)}
    )

def get_permissions_by_roleid(roleid: int, db: Session) -> dict:
    permissions = db.query(rbac.RoleModulePermission).filter(rbac.RoleModulePermission.roleId == roleid).all()
    return success_response(
        "Role module permissions retrieved successfully",
        {"permissions": [RoleModulePermissionResponse.model_validate(permission) for permission in permissions]}
    )

def update_role_module_permission(permission_id: int, permission_update: RoleModulePermissionUpdate, user, db: Session) -> dict:
    permission = db.query(rbac.RoleModulePermission).filter(rbac.RoleModulePermission.id == permission_id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Permission with id {permission_id} not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )
    user_id = user.get("sub")
    if permission_update.create is not None:
        setattr(permission, 'create', bool(permission_update.create))
    if permission_update.update is not None:
        setattr(permission, 'update', bool(permission_update.update))
    if permission_update.delete is not None:
        setattr(permission, 'delete', bool(permission_update.delete))
    if permission_update.view is not None:
        setattr(permission, 'view', bool(permission_update.view))
    setattr(permission, 'updatedBy', user_id)
    db.commit()
    db.refresh(permission)
    return success_response(
        "Role module permission updated successfully",
        {"permission": RoleModulePermissionResponse.model_validate(permission)}
    )

def delete_role_module_permission(role_uuid: str, module_uuid: str, db: Session):
    module = db.query(rbac.Module).filter(rbac.Module.uuid == module_uuid).first()
    
    if not module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Module with uuid '{module_uuid}' not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )

    role = db.query(rbac.Role).filter(rbac.Role.uuid == role_uuid).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Role with uuid '{role_uuid}' not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )

    permission = db.query(rbac.RoleModulePermission).filter(rbac.RoleModulePermission.roleId == role.id, rbac.RoleModulePermission.moduleId == module.id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Permission for role '{role.name}' and module '{module.name}' not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )

    db.delete(permission)
    db.commit()

    return success_response(f"Permission for role '{role.name}' and module '{module.name}' deleted successfully")

# -------------------------
# Advanced RBAC APIs
# -------------------------

def get_role_with_all_permissions(roleid: int, db: Session):
    """Get a role with all its permissions"""
    from schemas.rbac import RoleWithPermissions

    role = db.query(rbac.Role).filter(rbac.Role.id == roleid).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"Role with id {roleid} not found",
                "status_code": status.HTTP_404_NOT_FOUND
            }
        )

    permissions = db.query(rbac.RoleModulePermission).filter(rbac.RoleModulePermission.roleId == roleid).all()

    return RoleWithPermissions(
        role=RoleResponse.model_validate(role),
        permissions=[RoleModulePermissionResponse.model_validate(p) for p in permissions]
    )

def get_module_with_all_permissions(moduleid: int, db: Session):
    """Get a module with all roles that have permissions on it"""
    from schemas.rbac import ModuleWithAllPermissions

    module = db.query(rbac.Module).filter(rbac.Module.id == moduleid).first()
    if not module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Module with id {moduleid} not found"
        )

    role_permissions = db.query(rbac.RoleModulePermission).filter(rbac.RoleModulePermission.moduleId == moduleid).all()

    return ModuleWithAllPermissions(
        module=ModuleResponse.model_validate(module),
        rolePermissions=[RoleModulePermissionResponse.model_validate(p) for p in role_permissions]
    )

def get_user_permission_summary(roleids: List[int], db: Session):
    """Get a comprehensive permission summary for given role IDs"""
    from schemas.rbac import PermissionSummary
    if not roleids:
        return []
    permissions = db.query(rbac.RoleModulePermission).filter(rbac.RoleModulePermission.roleId.in_(roleids)).all()
    permission_map = {}
    for perm in permissions:
        key = f"{perm.roleId}_{perm.moduleId}"
        if key not in permission_map:
            role = db.query(rbac.Role).filter(rbac.Role.id == perm.roleId).first()
            module = db.query(rbac.Module).filter(rbac.Module.id == perm.moduleId).first()
            permission_map[key] = PermissionSummary(
                roleName=str(getattr(role, 'name', 'Unknown')) if role else "Unknown",
                moduleName=str(getattr(module, 'name', 'Unknown')) if module else "Unknown",
                create=bool(getattr(perm, 'create', False)),
                update=bool(getattr(perm, 'update', False)),
                delete=bool(getattr(perm, 'delete', False)),
                view=bool(getattr(perm, 'view', False))
            )
    return list(permission_map.values())

def upsert_role_with_permissions(request_data: dict, user, db: Session):
    role_data = request_data.get("role", {})
    permissions_data = request_data.get("permissions", {})

    role_name = role_data.get("name")
    role_slug = role_data.get("slug")
    role_description = role_data.get("description", None)
    if not role_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Role name is required",
                "status_code": status.HTTP_400_BAD_REQUEST
            }
        )

    user_id = user.get("sub")
    errors = []
    successful_permissions = {}

    # Step 1: Upsert the role
    # Try to find role by slug first, then by name if slug is not provided
    if role_slug:
        existing_role = db.query(rbac.Role).filter(rbac.Role.slug == role_slug).first()
    else:
        existing_role = db.query(rbac.Role).filter(rbac.Role.name == role_name).first()

    if existing_role:
        # Update existing role
        existing_role.updatedBy = user_id
        if role_slug: existing_role.slug = role_slug  # Update slug if provided
        if role_description: existing_role.description = role_description  # Update description if provided
        role = existing_role
    else:
        # Create new role
        role = rbac.Role(
            name=role_name,
            slug=role_slug,
            description=role_description,
            createdBy=user_id,
            updatedBy=user_id
        )
        db.add(role)

    # Commit role changes to get the role ID
    db.commit()
    db.refresh(role)

    # Step 2: Process permissions for each module
    for module_name, module_permissions in permissions_data.items():
        try:
            # Find module by name
            module = db.query(rbac.Module).filter(rbac.Module.slug == module_name).first()
            if not module:
                errors.append(f"Permission setting failed for module '{module_name}': Module does not exist")
                continue

            # Check if permission already exists
            existing_permission = db.query(rbac.RoleModulePermission).filter(
                rbac.RoleModulePermission.roleId == role.id,
                rbac.RoleModulePermission.moduleId == module.id
            ).first()

            # Extract permission values
            view = module_permissions.get("view", False)
            create = module_permissions.get("create", False)
            update = module_permissions.get("update", False)
            delete = module_permissions.get("delete", False)

            if existing_permission:
                # Update existing permissions (replace with new values)
                existing_permission.view = view
                existing_permission.create = create
                existing_permission.update = update
                existing_permission.delete = delete
                existing_permission.updatedBy = user_id

                # Store the updated permissions for response
                successful_permissions[module_name] = {
                    "view": existing_permission.view,
                    "create": existing_permission.create,
                    "update": existing_permission.update,
                    "delete": existing_permission.delete
                }
            else:
                # Create new permission
                new_permission = rbac.RoleModulePermission(
                    roleId=role.id,
                    moduleId=module.id,
                    view=view,
                    create=create,
                    update=update,
                    delete=delete,
                    createdBy=user_id,
                    updatedBy=user_id
                )
                db.add(new_permission)

                # Store the new permissions for response
                successful_permissions[module_name] = {
                    "view": view,
                    "create": create,
                    "update": update,
                    "delete": delete
                }

        except Exception as e:
            errors.append(f"Permission setting failed for module '{module_name}': {str(e)}")
            continue

    # Commit all permission changes
    try:
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": f"Failed to save permissions: {str(e)}",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

    return {
        "role": role_name,
        "permissions": successful_permissions,
        "errors": errors if errors else None
    }
