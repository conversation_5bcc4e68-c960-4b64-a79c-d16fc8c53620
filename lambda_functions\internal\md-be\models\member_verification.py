from sqlalchemy import Column, Integer, String, Enum, DateTime, ForeignKey, func, text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.ext.mutable import MutableList
from db.db import Base
import enum

class VerificationStatus(enum.Enum):
    pending = "pending"
    verified = "verified"
    rejected = "rejected"
    under_review = "under_review"

class MemberVerification(Base):
    __tablename__ = "member_verification"

    member_uuid = Column("member_uuid", UUID(as_uuid=True), ForeignKey("co_members.uuid"), primary_key=True)
    verification_status = Column("verification_status", Enum(VerificationStatus), nullable=False, default=VerificationStatus.pending)
    verification_data = Column(
    "verification_data",
    MutableList.as_mutable(JSONB),
    nullable=False,
    default=list
)
    dateCreated = Column("date_created", DateTime, default=func.now())
    dateUpdated = Column("date_updated", DateTime, default=func.now(), onupdate=func.now())
    createBy = Column("create_by", UUID(as_uuid=True), nullable=True)
    updatedBy = Column("updated_by", UUID(as_uuid=True), nullable=True)

    # Relationship with CoMember
    member = relationship("CoMember", backref="verification")