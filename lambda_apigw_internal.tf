resource "aws_vpc_endpoint" "api_gw" {
  vpc_id            = data.terraform_remote_state.vpc.outputs.vpc_id
  service_name      = "com.amazonaws.${var.aws_region}.execute-api"
  vpc_endpoint_type = "Interface"
  subnet_ids        = data.terraform_remote_state.vpc.outputs.stg_private_subnets
  security_group_ids = [data.terraform_remote_state.vpc.outputs.sg_ops_base]

  private_dns_enabled = true
  tags = merge(module.label.tags, { Name = "${var.function_name}-gateway-endpoint" })
}

resource "aws_api_gateway_rest_api" "lambda-md-be-apigw" {
  name        = "${var.function_name}-gateway"
  description = "Private REST API Gateway for Lambda"
  endpoint_configuration {
    types = ["PRIVATE"]
  }
}

resource "aws_api_gateway_resource" "root" {
  rest_api_id = aws_api_gateway_rest_api.lambda-md-be-apigw.id
  parent_id   = aws_api_gateway_rest_api.lambda-md-be-apigw.root_resource_id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "proxy" {
  rest_api_id   = aws_api_gateway_rest_api.lambda-md-be-apigw.id
  resource_id   = aws_api_gateway_resource.root.id
  http_method   = "ANY"
  authorization = "NONE"
  request_parameters = {
    "method.request.path.proxy" = true
  }
}

resource "aws_api_gateway_method" "root_any" {
  rest_api_id   = aws_api_gateway_rest_api.lambda-md-be-apigw.id
  resource_id   = aws_api_gateway_rest_api.lambda-md-be-apigw.root_resource_id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "lambda" {
  rest_api_id             = aws_api_gateway_rest_api.lambda-md-be-apigw.id
  resource_id             = aws_api_gateway_resource.root.id
  http_method             = aws_api_gateway_method.proxy.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = module.lambda-md-be.lambda_function_invoke_arn
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_parameters = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
}

resource "aws_api_gateway_integration" "root_lambda" {
  rest_api_id             = aws_api_gateway_rest_api.lambda-md-be-apigw.id
  resource_id             = aws_api_gateway_rest_api.lambda-md-be-apigw.root_resource_id
  http_method             = aws_api_gateway_method.root_any.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = module.lambda-md-be.lambda_function_invoke_arn
  passthrough_behavior    = "WHEN_NO_MATCH"
}

resource "aws_api_gateway_deployment" "lambda-md-be-apigw" {
  depends_on = [aws_api_gateway_integration.lambda]
  rest_api_id = aws_api_gateway_rest_api.lambda-md-be-apigw.id
  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.root.id,
      aws_api_gateway_method.proxy.id,
      aws_api_gateway_method.root_any.id,
      aws_api_gateway_integration.lambda.id,
      aws_api_gateway_integration.root_lambda.id
    ]))
  }
}

resource "aws_api_gateway_stage" "default" {
  rest_api_id   = aws_api_gateway_rest_api.lambda-md-be-apigw.id
  stage_name    = "default"
  deployment_id = aws_api_gateway_deployment.lambda-md-be-apigw.id

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.lamba_md_be_apigw_loggroup.arn
    format = jsonencode({
      requestId      = "$context.requestId",
      httpMethod     = "$context.httpMethod",
      resourcePath   = "$context.resourcePath",
      status         = "$context.status",
      integration    = "$context.integrationStatus",
      responseLength = "$context.responseLength"
    })
  }
}

resource "aws_api_gateway_rest_api_policy" "private_policy" {
  rest_api_id = aws_api_gateway_rest_api.lambda-md-be-apigw.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = "*"
        Action = "execute-api:Invoke"
        Resource = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.lambda-md-be-apigw.id}/*"
        Condition = {
          StringEquals = {
            "aws:SourceVpce" = aws_vpc_endpoint.api_gw.id
          }
        }
      }
    ]
  })
}

resource "aws_cloudwatch_log_group" "lamba_md_be_apigw_loggroup" {
  name              = "/aws/apigateway/${var.function_name}-gateway"
  retention_in_days = 7
}

module "lambda-md-be" {
  source          = "github.com/USChamber/terraform-aws-uschamber//modules/global/lambda"
  function_name   = var.function_name
  description     = var.description
  create_role     = true
  attach_network_policy = true
  attach_ecr_policy = true
  timeout         = var.timeout
  memory_size     = var.memory_size
  package_type    = var.package_type
  image_uri       = var.image_uri
  tags            = merge(module.label.tags, { Name = var.function_name })
  create_ecr_repo = false
  ecr_repo        = var.ecr_repo
  ecr_repo_tags   = merge(module.label.tags, { Name = var.ecr_repo })
  kms_key_arn     = aws_kms_key.lambda-md-be-kms.arn
  vpc_security_group_ids = [aws_security_group.lambda-md-be-sg.id]
  vpc_subnet_ids  = data.terraform_remote_state.vpc.outputs.stg_private_subnets
  create_current_version_allowed_triggers = false
  allowed_triggers = {
    APIGatewayAny = {
      service    = "apigateway"
      source_arn = "${aws_api_gateway_rest_api.lambda-md-be-apigw.execution_arn}/*/*"
    }
  }
}
