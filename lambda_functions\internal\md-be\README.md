# MD-BE

## Project Description

Backend for member management using Auth0 (for members) and AWS Cognito (for admins), FastAPI, and modular architecture.

## Setup Instructions

The API will be available at `http://localhost:8000`

## Setup Instructions

1. **Clone the repository:**

   ```bash
   git clone <your-repo-url>
   cd MD-BE
   ```

2. **Set up Python Virtual Environment:**

   ```bash
   # Create a new virtual environment
   python -m venv venv

   # Activate the virtual environment
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies:**

   ```bash
   # Make sure your virtual environment is activated (you should see (venv) in your terminal)
   pip install -r requirements.txt
   ```

4. **Create a `.env` file:**

   - Copy the example below and fill in your Auth0, Cognito, database, and JWT settings:

     ```env
     # Auth0 configuration (for members)
     DOMAIN=your-auth0-domain
     AUDIENCE=your-api-audience
     CLIENT_ID=your-auth0-client-id
     CLIENT_SECRET=your-auth0-client-secret

     # Cognito configuration (for admins)
     COGNITO_CLIENT_ID=your-cognito-app-client-id
     COGNITO_USER_POOL_ID=your-cognito-user-pool-id
     AWS_REGION=your-aws-region

     # OAuth Redirect URI
     REDIRECT_URI=<BASE_URL>/callback
     SCOPE=openid profile email

     # AWS Secrets Manager configuration
     AWS_SECRET_NAME=your-secret-name

     # Legacy Database configuration (optional)
     DATABASE_URL=postgresql://user:password@host:port/dbname
     ```

5. **Run the application:**

   ```bash
   # Make sure your virtual environment is activated
   python main.py
   ```

   The application will start on `http://localhost:8000`.

## Required Environment Variables

| Variable             | Description                                |
| -------------------- | ------------------------------------------ |
| DOMAIN               | Auth0 domain                               |
| AUDIENCE             | Auth0 API audience                         |
| CLIENT_ID            | Auth0 client ID                            |
| CLIENT_SECRET        | Auth0 client secret                        |
| COGNITO_CLIENT_ID    | Cognito App Client ID (for admins)         |
| COGNITO_USER_POOL_ID | Cognito User Pool ID (for admins)          |
| AWS_REGION           | AWS region for Cognito and Secrets Manager |
| AWS_SECRET_NAME      | Name of secret in AWS Secrets Manager      |
| RDS_HOST             | RDS database hostname                      |
| RDS_PORT             | RDS database port                          |
| RDS_USERNAME         | RDS database username                      |
| RDS_REGION           | AWS region for RDS                         |
| RDS_DB_NAME          | RDS database name                          |
| REDIRECT_URI         | OAuth redirect URI                         |
| SCOPE                | OAuth scopes (e.g., openid profile email)  |
| DATABASE_URL         | Legacy PostgreSQL connection string        |

## Project Structure

- `auth0_manage_api/` - Auth0 management API integration
- `controller/` - Controllers for calling respective services/apis
- `db/` - Database connection and models
- `dependencies/` - Dependencies for authentication and token handling
- `models/` - Pydantic models
- `routes/` - API route definitions
- `schemas/` - Data schemas
- `services/` - Service layer for core business logic
