from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Literal, Union
from uuid import UUID
from datetime import datetime
from models.member_verification import VerificationStatus

# Base schema for member verification
class MemberVerificationBase(BaseModel):
    verification_status: VerificationStatus = Field(
        VerificationStatus.pending,  # use Enum value as default
        description="Verification status of the member"
    )
    verification_data: Optional[Union[Dict[str, Any], List[Dict[str, Any]]]] = Field(
        None,
        description="Additional verification data in JSON format. Can be a single object (legacy) or list of objects (versioned)"
    )

# Schema for creating/updating member verification
class MemberVerificationUpsert(MemberVerificationBase):
    member_uuid: UUID = Field(..., description="UUID of the member to verify")
    
    class Config:
        from_attributes = True

# Schema for member verification response
class MemberVerificationResponse(MemberVerificationBase):
    member_uuid: UUID = Field(..., description="UUID of the member")
    dateCreated: datetime = Field(..., description="Creation timestamp")
    dateUpdated: datetime = Field(..., description="Last update timestamp")
    createBy: Optional[UUID] = Field(None, description="UUID of admin who created the verification")
    updatedBy: Optional[UUID] = Field(None, description="UUID of admin who last updated the verification")
    
    class Config:
        from_attributes = True

# Schema for member verification list response
class MemberVerificationListResponse(BaseModel):
    total_count: int = Field(..., description="Total number of verifications")
    verifications: List[MemberVerificationResponse] = Field(..., description="List of member verifications")
    
    class Config:
        from_attributes = True