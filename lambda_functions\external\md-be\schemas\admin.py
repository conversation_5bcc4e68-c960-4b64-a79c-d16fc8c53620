from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import List, Optional
from datetime import datetime
from uuid import UUID

class AdminCreate(BaseModel):
    email: EmailStr
    username: str
    password: str
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    phone: Optional[str] = None
    countryCode: Optional[str] = None
    roles: Optional[List[str]] = []

    @field_validator('email', mode='before')
    @classmethod
    def validate_email_field(cls, v):
        """Convert empty strings to None for email fields"""
        if v == '' or v is None:
            return None
        return v

class Admin_cognito_create(BaseModel):
    email: EmailStr
    username: str
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    phone: Optional[str] = None
    countryCode: Optional[str] = None
    roles: Optional[List[str]] = []

    @field_validator('email', mode='before')
    @classmethod
    def validate_email_field(cls, v):
        """Convert empty strings to None for email fields"""
        if v == '' or v is None:
            return None
        return v

class AdminUpdate(BaseModel):
    password: Optional[str] = None
    email: Optional[EmailStr] = None
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    phone: Optional[str] = None
    countryCode: Optional[str] = None
    isActive: Optional[bool] = None
    isTempPassword: Optional[bool] = None
    roles: Optional[List[str]] = None

    @field_validator('email', mode='before')
    @classmethod
    def validate_email_field(cls, v):
        """Convert empty strings to None for email fields"""
        if v == '' or v is None:
            return None
        return v

class Admin(BaseModel):
    email: EmailStr
    username: str
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    phone: Optional[str] = None
    countryCode: Optional[str] = None
    isActive: Optional[bool] = True
    isTempPassword: Optional[bool] = False
    emailVerified: Optional[bool] = False
    roles: Optional[List[str]] = []
    cognitoId: str
    createdBy: Optional[str] = None
    updatedBy: Optional[str] = None

    @field_validator('email', mode='before')
    @classmethod
    def validate_email_field(cls, v):
        """Convert empty strings to None for email fields"""
        if v == '' or v is None:
            return None
        return v

class AdminResponse(BaseModel):
    uuid: UUID
    email: EmailStr
    username: str
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    phone: Optional[str] = None
    countryCode: Optional[str] = None
    isActive: Optional[bool] = True
    isTempPassword: Optional[bool] = False
    emailVerified: Optional[bool] = False
    roles: Optional[List[str]] = []
    cognitoId: str
    createdBy: Optional[str] = None
    updatedBy: Optional[str] = None
    lastLogin: Optional[datetime] = None
    dateCreated: Optional[datetime] = None
    dateUpdated: Optional[datetime] = None

    @field_validator('email', mode='before')
    @classmethod
    def validate_email_field(cls, v):
        """Convert empty strings to None for email fields"""
        if v == '' or v is None:
            return None
        return v

    class Config:
        from_attributes = True

class AdminOut(BaseModel):
    """Legacy schema for backward compatibility"""
    uuid: str
    username: str
    email: Optional[str] = None
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    countryCode: Optional[str] = None
    roles: Optional[List[str]] = []
    dateCreated: Optional[datetime] = None
    dateUpdated: Optional[datetime] = None
    lastLogin: Optional[datetime] = None

    class Config:
        from_attributes = True

class ForgotPasswordRequest(BaseModel):
    username: str = Field(..., description="Username or email for password reset")

class ConfirmForgotPasswordRequest(BaseModel):
    username: str = Field(..., description="Username of the admin")
    confirmationCode: str = Field(..., description="Confirmation code received via email")
    newPassword: str = Field(..., min_length=8, description="New password")

class ForgotPasswordResponse(BaseModel):
    message: str
    success: bool = True

class ConfirmForgotPasswordResponse(BaseModel):
    message: str
    success: bool = True

class ChangePasswordRequest(BaseModel):
    accessToken: str = Field(..., description="Access token for password change")
    newPassword: str = Field(..., min_length=8, description="New password")

class ChangePasswordResponse(BaseModel):
    message: str
    success: bool = True
    username: str
