from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from models.member_verification import VerificationStatus

class MemberExportFilters(BaseModel):
    """Filters for member export functionality"""
    search: Optional[str] = Field(None, description="General search across multiple fields")
    firstName: Optional[str] = Field(None, description="Filter by first name (partial match)")
    lastName: Optional[str] = Field(None, description="Filter by last name (partial match)")
    email: Optional[str] = Field(None, description="Filter by email (partial match)")
    membershipTier: Optional[str] = Field(None, description="Filter by membership tier")
    communityStatus: Optional[str] = Field(None, description="Filter by community status")
    verificationStatus: Optional[VerificationStatus] = Field(None, description="Filter by verification status")
    organizationName: Optional[str] = Field(None, description="Filter by organization name (partial match)")
    organizationCity: Optional[str] = Field(None, description="Filter by organization city (partial match)")
    organizationState: Optional[str] = Field(None, description="Filter by organization state (partial match)")
    organizationZip: Optional[str] = Field(None, description="Filter by organization ZIP code (partial match)")
    companySize: Optional[str] = Field(None, description="Filter by company size")
    industry: Optional[str] = Field(None, description="Filter by industry (partial match)")
    dateCreatedFrom: Optional[str] = Field(None, description="Filter members created from this date (ISO format)")
    dateCreatedTo: Optional[str] = Field(None, description="Filter members created up to this date (ISO format)")
    
    # Legacy field names for backward compatibility
    city: Optional[str] = Field(None, description="Filter by city (partial match) - legacy field")
    state: Optional[str] = Field(None, description="Filter by state (partial match) - legacy field")
    zip: Optional[str] = Field(None, description="Filter by ZIP code (partial match) - legacy field")
    dateCreated: Optional[datetime] = Field(None, description="Filter by date created - legacy field")

class MemberExportRequest(BaseModel):
    """Request schema for member export endpoint"""
    filters: Optional[MemberExportFilters] = Field(default_factory=MemberExportFilters, description="Optional filters to apply")
    selectedFields: List[str] = Field(..., description="List of fields to include in export")
    notes: str = Field(..., description="Purpose/reason for the export")

    @validator('selectedFields')
    def validate_selected_fields(cls, v):
        if not v or len(v) == 0:
            raise ValueError('At least one selected field is required')
        
        # Define available fields that can be exported
        available_fields = {
            'firstName', 'lastName', 'loginEmail', 'personalBusinessEmail', 'phone',
            'professionalTitle', 'membershipTier', 'communityStatus', 'verificationStatus', 'dateCreated',
            'organizationName', 'city', 'state', 'zip', 'industry', 'companySize',
            'annualRevenue', 'yearFounded'
        }
        
        invalid_fields = set(v) - available_fields
        if invalid_fields:
            raise ValueError(f'Invalid fields: {", ".join(invalid_fields)}. Available fields: {", ".join(sorted(available_fields))}')
        
        return v

    @validator('notes')
    def validate_notes(cls, v):
        if not v or v.strip() == '':
            raise ValueError('Notes cannot be empty')
        return v.strip()

    class Config:
        from_attributes = True 