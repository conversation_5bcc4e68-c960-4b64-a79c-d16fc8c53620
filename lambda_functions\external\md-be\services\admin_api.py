
import datetime
import re
import boto3
import random
import string
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from sqlalchemy.inspection import inspect
from models.admin import AdminModel, UsedPasswordTokenModel
from schemas.admin import Admin_cognito_create, AdminUpdate, AdminResponse
from config import settings
from models.member import CoMember, CoAuth0User
from jose import jwt
from schemas.member import CoMemberCreate, CoMemberUpdateByAdmin, CoMemberResponse
from models.rbac import RoleModulePermission, Role, Module
from utils.response_utils import (
    success_response, created_response, conflict_response
)
from utils.email_service import send_admin_welcome_email_with_password_change
from utils.custom_jwt_utils import generate_password_change_token, validate_password_change_token, get_token_hash, get_token_expiration
from utils.cloudwatch_logger import cloudwatch_logger
import logging
from auth0_manage_api.auth0_manage import get_management_token
from auth0.management import Auth0
from models.member_verification import MemberVerification, VerificationStatus
from typing import Optional

logger = logging.getLogger(__name__)

client = boto3.client(
    'cognito-idp',
    region_name=settings.AWS_REGION
)

def generate_random_password(length=12):
    if length < 8:
        length = 8

    # Define character sets
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"

    password = [
        random.choice(lowercase),   # Ensure at least one lowercase
        random.choice(uppercase),   # Ensure at least one uppercase
        random.choice(digits),      # Ensure at least one digit
        random.choice(special_chars) # Ensure at least one special character
    ]

    all_chars = lowercase + uppercase + digits + special_chars
    for _ in range(length - 4):  # Changed from 3 to 4 since we now have 4 required chars
        password.append(random.choice(all_chars))

    random.shuffle(password)

    return ''.join(password)

def create_member(member: CoMemberCreate, new_user_and_token: dict, admin_user_payload: dict, db: Session):
    try:
        # Manually check required fields (optional if enforced by schema, but extra safe)
        required_fields = {
            "loginEmail": member.loginEmail,
            "password": member.password,
        }

        missing = [field for field, value in required_fields.items() if not value]
        if missing:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "message": f"Missing required field(s): {', '.join(missing)}",
                    "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY
                }
            )

        # Check if member already exists
        if db.query(CoMember).filter_by(auth0Id=new_user_and_token['new_user']['user_id']).first():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": "User already registered.",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )

        new_member = CoMember(
            auth0Id=new_user_and_token['new_user']['user_id'],
            loginEmail=member.loginEmail,
            customerIoId=member.customerIoId,
            openWaterId=member.openWaterId,
            firstName=member.firstName,
            lastName=member.lastName,
            loginEmailVerified=member.loginEmailVerified,
            identityType=member.identityType,
            personalBusinessEmail=member.personalBusinessEmail,
            phone=member.phone,
            professionalTitle=member.professionalTitle,
            membershipTier=member.membershipTier,
            communityStatus=member.communityStatus,
        )
        
        # Adding data to CoAuth0User table
        user = new_user_and_token["new_user"]

        new_member_auth0_users = CoAuth0User(
            userId=user["user_id"],
            email=user.get("email"),
            name=user.get("name"),
            familyName=user.get("familyName"),
            givenName=user.get("given_name"),
            nickName=user.get("nickname"),
            picture=user.get("picture"),
            emailVerified=user.get("email_verified"),
            identities=user.get("identities"),
            createdAt=user.get("created_at"),
            updatedAt=user.get("updated_at")
        )
        
        # Set created/updated by fields using UUIDs
        if admin_user_payload.get("sub"):
            new_member.createdByAdmin = admin_user_payload["sub"]
            new_member.updatedByAdmin = admin_user_payload["sub"]

        db.add(new_member)
        db.add(new_member_auth0_users)
        db.commit()
        db.refresh(new_member)
        db.refresh(new_member_auth0_users)

        admin_verification = MemberVerification(
            member_uuid=new_member.uuid,
            verification_status="pending"
        )
        db.add(admin_verification)

        # For self-registration, set the created/updated by fields to the new member's own UUID
        if not admin_user_payload.get("sub"):
            new_member.createdByMember = new_member.uuid
            new_member.updatedByMember = new_member.uuid
            db.commit()
            db.refresh(new_member)

        return created_response(
            "Member created successfully"
        )

    except HTTPException as http_exc:
        raise http_exc

    except SQLAlchemyError as db_err:
        db.rollback()
        logging.exception("Database error during registration:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "A database error occurred. Please try again later.",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

    except Exception as e:
        logging.exception("Unexpected error during registration:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration.",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def update_member_by_uuid(uuid: str, member: CoMemberUpdateByAdmin, db: Session, admin_user_payload: Optional[dict] = None):
    existing_member = db.query(CoMember).filter_by(uuid=uuid).first()
    if not existing_member:
        raise HTTPException(status_code=404, detail="No member found")
    co_auth0_user = db.query(CoAuth0User).filter_by(userId=existing_member.auth0Id).first()
    
    # Get admin user information for logging
    admin_user_info = None
    user_id = None
    if admin_user_payload:
        user_id = admin_user_payload.get("user_id") or admin_user_payload.get("sub")
        # Get admin user details from database
        admin_user = db.query(AdminModel).filter_by(cognitoId=user_id).first()
        if admin_user:
            admin_user_info = {
                "admin_uuid": str(admin_user.uuid),
                "admin_username": admin_user.username,
                "admin_email": admin_user.email,
                "admin_cognito_id": user_id
            }
    
    # Get verification status for before state
    existing_verification = db.query(MemberVerification).filter_by(member_uuid=existing_member.uuid).first()
    verification_status = existing_verification.verification_status if existing_verification else None
    
    # Log before state
    before_state = {
        "uuid": str(existing_member.uuid),
        "firstName": existing_member.firstName,
        "lastName": existing_member.lastName,
        "loginEmail": existing_member.loginEmail,
        "personalBusinessEmail": existing_member.personalBusinessEmail,
        "membershipTier": existing_member.membershipTier,
        "communityStatus": existing_member.communityStatus,
        "identityType": existing_member.identityType,
        "auth0Id": existing_member.auth0Id,
        "verificationStatus": str(verification_status) if verification_status else None,
        "updatedByAdmin": str(existing_member.updatedByAdmin) if existing_member.updatedByAdmin else None,
        "updatedByMember": str(existing_member.updatedByMember) if existing_member.updatedByMember else None,
        "dateUpdated": existing_member.dateUpdated.isoformat() if existing_member.dateUpdated else None
    }
    
    cloudwatch_logger.log_api_operation(
        operation="admin_update",
        resource_id=str(uuid),
        resource_type="member",
        before_state=before_state,
        user_id=user_id,
        additional_context={
            "update_data": member.model_dump(exclude_unset=True),
            "auth0_user_id": existing_member.auth0Id,
            "admin_operation": True,
            "admin_user_info": admin_user_info
        }
    )
    
    try:
        # Update user in Auth0 only if auth0Id exists (email and password must be updated separately)
        if existing_member.auth0Id is not None:
            domain = settings.DOMAIN
            token = get_management_token()

            if not domain or not token:
                raise HTTPException(
                    status_code=500,
                    detail="Auth0 configuration error: domain or token not available"
                )

            auth0 = Auth0(domain, token)

            try:
                # Update password first if provided
                if member.password:
                    auth0.users.update(
                        id=str(existing_member.auth0Id),
                        body={
                            "password": member.password
                        }
                    )

                # Update email and name second (only if email has changed)
                if member.loginEmail and member.loginEmail != existing_member.loginEmail:
                    updated_user = auth0.users.update(
                        id=str(existing_member.auth0Id),
                        body={
                            "email": member.loginEmail,
                            "name": member.loginEmail
                        }
                    )

                    if updated_user:                    
                        updated_member_auth0_users = CoAuth0User(
                            userId=updated_user["user_id"],
                            email=updated_user.get("email"),
                            name=updated_user.get("name"),
                            familyName=updated_user.get("familyName"),
                            givenName=updated_user.get("given_name"),
                            nickName=updated_user.get("nickname"),
                            picture=updated_user.get("picture"),
                            emailVerified=updated_user.get("email_verified"),
                            identities=updated_user.get("identities"),
                            createdAt=updated_user.get("created_at"),
                            updatedAt=updated_user.get("updated_at")
                        )
                        
                        # update the co_auth0_user table in database
                        for column in inspect(CoAuth0User).attrs:
                            key = column.key
                            value = getattr(updated_member_auth0_users, key, None)
                            if hasattr(co_auth0_user, key) and value is not None:
                                setattr(co_auth0_user, key, value)
                                
                # update member verification status in member_verification table
                existing_verification = (
                    db.query(MemberVerification)
                    .filter_by(member_uuid=existing_member.uuid)
                    .first()
                )

                try:
                    if isinstance(member.verificationStatus, str):
                        # For string input, try to get the enum member
                        status = VerificationStatus[member.verificationStatus.lower()]
                    elif isinstance(member.verificationStatus, VerificationStatus):
                        status = member.verificationStatus
                    else:
                        status = VerificationStatus.pending
                except (KeyError, ValueError):
                    # If anything goes wrong, use the default
                    status = VerificationStatus.pending

                verification_data = {
                    'member_uuid': existing_member.uuid,
                    'verification_status': status,
                }

                if existing_verification:
                    # Update using setattr for SQLAlchemy
                    for key, value in verification_data.items():
                        setattr(existing_verification, key, value)
                else:
                    # Create new instance
                    new_verification = MemberVerification(**verification_data)
                    db.add(new_verification)

            except Exception as auth0_error:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to update user in Auth0: {str(auth0_error)}"
                )

        # Update member fields in database
        for key, value in member.model_dump(exclude_unset=True).items():
            if hasattr(existing_member, key):
                setattr(existing_member, key, value)
        
        # Set updated by fields
        if admin_user_payload and admin_user_payload.get("user_type") == "admin":
            admin = db.query(AdminModel).filter_by(cognitoId=admin_user_payload["user_id"]).first()
            if admin:
                existing_member.updatedByAdmin = admin.uuid
                
        elif(admin_user_payload and admin_user_payload.get("user_type") == "member"):
            existing_member.updatedByMember = existing_member.uuid
            
    except Exception as e:
        # Log error state
        cloudwatch_logger.log_api_operation(
            operation="admin_update",
            resource_id=str(uuid),
            resource_type="member",
            user_id=user_id,
            additional_context={
                "error_type": type(e).__name__,
                "auth0_user_id": existing_member.auth0Id if existing_member else None,
                "admin_operation": True,
                "admin_user_info": admin_user_info
            },
            error=str(e)
        )
        raise
        
    db.commit()
    db.refresh(existing_member)
    if co_auth0_user:
        db.refresh(co_auth0_user)

    # Get updated verification status for after state
    updated_verification = db.query(MemberVerification).filter_by(member_uuid=existing_member.uuid).first()
    updated_verification_status = updated_verification.verification_status if updated_verification else None

    # Log after state
    after_state = {
        "uuid": str(existing_member.uuid),
        "firstName": existing_member.firstName,
        "lastName": existing_member.lastName,
        "loginEmail": existing_member.loginEmail,
        "personalBusinessEmail": existing_member.personalBusinessEmail,
        "membershipTier": existing_member.membershipTier,
        "communityStatus": existing_member.communityStatus,
        "identityType": existing_member.identityType,
        "auth0Id": existing_member.auth0Id,
        "verificationStatus": str(updated_verification_status) if updated_verification_status else None,
        "updatedByAdmin": str(existing_member.updatedByAdmin) if existing_member.updatedByAdmin else None,
        "updatedByMember": str(existing_member.updatedByMember) if existing_member.updatedByMember else None,
        "dateUpdated": existing_member.dateUpdated.isoformat() if existing_member.dateUpdated else None
    }
    
    cloudwatch_logger.log_api_operation(
        operation="admin_update",
        resource_id=str(uuid),
        resource_type="member",
        after_state=after_state,
        user_id=user_id,
        additional_context={
            "operation_status": "success",
            "auth0_user_id": existing_member.auth0Id,
            "admin_operation": True,
            "admin_user_info": admin_user_info
        }
    )

    return success_response(
        "Member updated successfully",
        {"member": CoMemberResponse.model_validate(existing_member)}
    )


def create_admin_cognito(token: str, new_admin: Admin_cognito_create, db):
    try:
        decoded = jwt.get_unverified_claims(token)
        createdBy = decoded.get('sub') or decoded.get('cognitoid') or decoded.get('id')
        updatedBy = createdBy
        if not createdBy:
            raise HTTPException(status_code=400, detail={"message": "Invalid token: missing admin id for audit fields."})
 
        try:
            temp_password = generate_random_password()

            cognito_resp = client.admin_create_user(
                UserPoolId=settings.COGNITO_USER_POOL_ID,
                Username=new_admin.username,
                UserAttributes=[
                    {'Name': 'email', 'Value': new_admin.email},
                    {'Name': 'preferred_username', 'Value': new_admin.username},
                    {"Name": "email_verified", "Value": "true"}
                ],
                TemporaryPassword=temp_password,
                MessageAction='SUPPRESS',
                DesiredDeliveryMediums=[] 
            )
            sub = next(attr['Value'] for attr in cognito_resp['User']['Attributes'] if attr['Name'] == 'sub')
            cognitoId = sub
            
            # Generate custom JWT token for password change (24 hours)
            try:
                access_token = generate_password_change_token(new_admin.username, expires_in_hours=24)
            except Exception as e:
                logger.error(f"Failed to generate password change token: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to generate password change token: {str(e)}")
        except client.exceptions.UsernameExistsException:
            raise HTTPException(status_code=400, detail="User already exists in Cognito.")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Cognito sign up failed: {str(e)}")
 
        # new admin in the PGDB
        new_admin_db = AdminModel(
            email=new_admin.email,
            username=new_admin.username,
            firstName=new_admin.firstName,
            lastName=new_admin.lastName,
            phone=new_admin.phone,
            countryCode=new_admin.countryCode,
            cognitoId=cognitoId,
            createdBy=createdBy,
            updatedBy=updatedBy,
            roles=new_admin.roles,
            isActive=True,
            isTempPassword=True,  # Password is temporary - user needs to change it
            emailVerified=True,  # Since we set email_verified=true in Cognito
            lastLogin=datetime.datetime.now(datetime.timezone.utc),
        )
        db.add(new_admin_db)
        db.commit()
        db.refresh(new_admin_db)
        
        # Send welcome email to the new admin with password change link
        email_sent = False  # Initialize email_sent variable
        try:
            admin_email_data = {
                'email': new_admin_db.email,
                'username': new_admin_db.username,
                'firstName': new_admin_db.firstName if new_admin_db.firstName is not None else '',
                'lastName': new_admin_db.lastName if new_admin_db.lastName is not None else '',
                'roles': new_admin_db.roles if new_admin_db.roles is not None else ['Administrator'],
                'accessToken': access_token
            }
            email_sent = send_admin_welcome_email_with_password_change(admin_email_data)

            if email_sent:
                logger.info(f"Welcome email sent successfully to {new_admin_db.email}")
            else:
                logger.warning(f"Failed to send welcome email to {new_admin_db.email}")

        except Exception as e:
            logger.error(f"Error sending welcome email to {new_admin_db.email}: {str(e)}")
            email_sent = False  # Ensure email_sent is False on exception
            # Don't fail the admin creation if email sending fails
        
        return created_response(
            "Admin created in Cognito and DB. Welcome email with password change link sent.",
            {
                "uuid": str(new_admin_db.uuid),
                "username": new_admin_db.username,
                "email": new_admin_db.email,
                "emailSent": email_sent,
                "isTempPassword": True
            }
        )
    except Exception as e:
        db.rollback()
        error_message = str(e)
        
        if re.search(r"user already exists.*cognito", error_message, re.IGNORECASE):
            return conflict_response("User already exists in Cognito.")
        
        raise HTTPException(status_code=500, detail={"message": f"Error creating admin: {error_message}"})

        
def get_admin_list(db, page: int, pageSize: int):
    try:
        # Get total count
        total_count = db.query(AdminModel).count()
        
        # Get paginated results
        admins = db.query(AdminModel).offset((page - 1) * pageSize).limit(pageSize).all()
        
        # Return empty response with pagination info if no admins found
        if not admins:
            return success_response("No admins found.", {
                "admins": [],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": page,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": page * pageSize < total_count,
                    "hasPrevious": page > 1
                }
            })
        
        admin_list = []
        for admin in admins:
            admin_list.append({
                "uuid": admin.uuid,
                "username": admin.username,
                "email": admin.email,
                "firstName": admin.firstName,
                "lastName": admin.lastName,
                "phone": admin.phone,
                "countryCode": admin.countryCode,
                "isActive": admin.isActive,
                "isTempPassword": admin.isTempPassword,
                "emailVerified": admin.emailVerified,
                "roles": admin.roles,
                "cognitoId": admin.cognitoId,
                "createdBy": {
                    "uuid": admin.createdBy,
                    # fetch the username of the creator safely
                    "username": (
                        creator.username if (creator := db.query(AdminModel).filter(AdminModel.cognitoId == admin.createdBy).first())
                        else "unknown"
                    ) if admin.createdBy else "unknown"
                },
                "updatedBy": admin.updatedBy,
                "lastLogin": admin.lastLogin,
                "dateCreated": admin.dateCreated,
                "dateUpdated": admin.dateUpdated
            })
        
        return success_response("Admin list fetched successfully.", {
            "admins": admin_list,
            "pagination": {
                "totalCount": total_count,
                "currentPage": page,
                "pageSize": pageSize,
                "totalPages": (total_count + pageSize - 1) // pageSize,
                "hasNext": page * pageSize < total_count,
                "hasPrevious": page > 1
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error fetching admin list: {str(e)}"})


def get_admin_by_uuid(admin_uuid: str, db):
    """Get admin by UUID"""
    try:
        admin = db.query(AdminModel).filter(AdminModel.uuid == admin_uuid).first()
        if not admin:
            raise HTTPException(status_code=404, detail={"message": f"Admin with uuid {admin_uuid} not found"})

        return success_response(
            "Admin fetched successfully.",
            {"admin": AdminResponse.model_validate(admin)}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error fetching admin: {str(e)}"})

def get_admin_user(admin_user_payload: dict, db: Session):
    """Get admin user information"""
    try:
        admin = db.query(AdminModel).filter(AdminModel.cognitoId == admin_user_payload["sub"]).first()
        if not admin:
            raise HTTPException(status_code=404, detail={"message": f"Admin not found"})

        role_name = admin.roles[0]
        
        role = db.query(Role).filter(Role.slug == role_name).first()

        if not role:
            raise HTTPException(status_code=404, detail={"message": f"Role '{role_name}' not found for admin."})

        permissions = db.query(RoleModulePermission).filter(RoleModulePermission.roleId == role.id).all()

        modules = db.query(Module).filter(Module.id.in_([perm.moduleId for perm in permissions])).all()

        permissions = [{module.slug: {
            "create": perm.create,
            "update": perm.update,
            "delete": perm.delete,
            "view": perm.view
        }} for module, perm in zip(modules, permissions)]

        return success_response(
            "Admin user and permissions fetched successfully.",
            {
                "user": {
                    "uuid": admin.uuid,
                    "username": admin.username,
                    "email": admin.email,
                    "firstName": admin.firstName,
                    "lastName": admin.lastName,
                    "phone": admin.phone,
                    "countryCode": admin.countryCode,
                    "isActive": admin.isActive,
                    "isTempPassword": admin.isTempPassword,
                    "emailVerified": admin.emailVerified,
                    "roles": role_name,
                    "createdBy": admin.createdBy,
                    "permissions": permissions
                }
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error fetching admin user: {str(e)}"})

def update_admin(uuid: str, admin: AdminUpdate, db: Session, admin_user_payload: dict):
    try:
        existing_admin = db.query(AdminModel).filter(AdminModel.uuid == uuid).first()  # use uuid instead of admin_uuid for consistency with other functions, but it's not required
        if not existing_admin:
            raise HTTPException(status_code=404, detail="Admin not found")
        
        username = existing_admin.username
        
        if admin.password or admin.email:  # if either password or email is provided, update both in Cognito
            try:
                if admin.password:  # if password is provided, update it in Cognito
                    client.admin_set_user_password(
                        UserPoolId=settings.COGNITO_USER_POOL_ID,
                        Username=username,
                        Password=admin.password,
                        Permanent=True
                    )
                    
                if admin.email:  # if email is provided, update it in Cognito
                    client.admin_update_user_attributes(
                        UserPoolId=settings.COGNITO_USER_POOL_ID,
                        Username=username,
                        UserAttributes=[
                            {
                                'Name': 'email',
                                'Value': admin.email
                            }
                        ]
                    )
                    
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Cognito email update failed: {str(e)}")

        # remove password from update to avoid saving it in the DB
        admin_dict = admin.dict(exclude_unset=True)
        admin_dict.pop("password", None)
        
        admin_dict["updatedBy"] = admin_user_payload["sub"]
        admin_dict["dateUpdated"] = datetime.datetime.now(datetime.timezone.utc)
        
        # Update the existing admin with the new values
        for key, value in admin_dict.items():
            setattr(existing_admin, key, value)

        db.commit()
        db.refresh(existing_admin)

        return success_response(
            "Admin updated successfully",
            {"admin": AdminResponse.model_validate(existing_admin)}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error updating admin: {str(e)}"})

def delete_admin(uuid: str, db: Session, admin_user_payload: dict):
    """Delete admin from both database and Cognito"""
    try:
        # Find the admin to delete
        admin_to_delete = db.query(AdminModel).filter(AdminModel.uuid == uuid).first()
        if not admin_to_delete:
            raise HTTPException(status_code=404, detail={"message": f"Admin with uuid {uuid} not found"})
        
        username = admin_to_delete.username
        
        # Delete from Cognito first
        try:
            client.admin_delete_user(
                UserPoolId=settings.COGNITO_USER_POOL_ID,
                Username=username
            )
        except client.exceptions.UserNotFoundException:
            # User might already be deleted from Cognito, continue with DB deletion
            pass
        except Exception as e:
            raise HTTPException(status_code=500, detail={"message": f"Cognito deletion failed: {str(e)}"})
        
        # Delete from database
        db.delete(admin_to_delete)
        db.commit()
        
        return success_response(
            f"Admin '{username}' deleted successfully from both Cognito and database."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail={"message": f"Error deleting admin: {str(e)}"})


def initiate_forgot_password(username: str, db: Session):
    """Initiate forgot password flow for admin user"""
    try:
        # First, check if the admin exists in our database
        admin = db.query(AdminModel).filter(
            (AdminModel.username == username) | (AdminModel.email == username)
        ).first()
        
        if not admin:
            raise HTTPException(status_code=404, detail={"message": "Admin not found"})
        
        # Use the actual username (not email) for Cognito operations
        cognito_username = admin.username
        
        try:
            # Initiate forgot password with Cognito
            response = client.forgot_password(
                ClientId=settings.COGNITO_CLIENT_ID,
                Username=cognito_username
            )
            
            return success_response(
                "Password reset code sent successfully. Please check your email.",
                {
                    "username": cognito_username,
                    "codeDeliveryDetails": {
                        "destination": response.get('CodeDeliveryDetails', {}).get('Destination', ''),
                        "deliveryMedium": response.get('CodeDeliveryDetails', {}).get('DeliveryMedium', 'EMAIL')
                    }
                }
            )
            
        except client.exceptions.UserNotFoundException:
            raise HTTPException(status_code=404, detail={"message": "Admin not found in Cognito"})
        except client.exceptions.InvalidParameterException as e:
            raise HTTPException(status_code=400, detail={"message": f"Invalid request: {str(e)}"})
        except client.exceptions.LimitExceededException:
            raise HTTPException(status_code=429, detail={"message": "Too many requests. Please try again later."})
        except Exception as e:
            raise HTTPException(status_code=500, detail={"message": f"Cognito forgot password failed: {str(e)}"})
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail={"message": f"Error initiating forgot password: {str(e)}"})


def confirm_forgot_password(username: str, confirmation_code: str, new_password: str, db: Session):
    """Confirm forgot password with verification code and set new password"""
    try:
        # Check if the admin exists in our database
        admin = db.query(AdminModel).filter(
            (AdminModel.username == username) | (AdminModel.email == username)
        ).first()
        
        if not admin:
            raise HTTPException(status_code=404, detail={"message": "Admin not found"})
        
        # Use the actual username (not email) for Cognito operations
        cognito_username = admin.username
        
        try:
            # Confirm forgot password with Cognito
            client.confirm_forgot_password(
                ClientId=settings.COGNITO_CLIENT_ID,
                Username=cognito_username,
                ConfirmationCode=confirmation_code,
                Password=new_password
            )
            # Update admin in database to reflect password change
            setattr(admin, "isTempPassword", False)
            setattr(admin, "updatedBy", admin.cognitoId)  # Self-updated
            setattr(admin, "dateUpdated", datetime.datetime.now(datetime.timezone.utc))
            
            db.commit()
            
            return success_response(
                "Password reset successful. You can now login with your new password.",
                {
                    "username": cognito_username,
                    "passwordChanged": True
                }
            )
            
        except client.exceptions.CodeMismatchException:
            raise HTTPException(status_code=400, detail={"message": "Invalid verification code"})
        except client.exceptions.ExpiredCodeException:
            raise HTTPException(status_code=400, detail={"message": "Verification code has expired. Please request a new code."})
        except client.exceptions.InvalidPasswordException as e:
            raise HTTPException(status_code=400, detail={"message": f"Invalid password: {str(e)}"})
        except client.exceptions.UserNotFoundException:
            raise HTTPException(status_code=404, detail={"message": "Admin not found in Cognito"})
        except client.exceptions.InvalidParameterException as e:
            raise HTTPException(status_code=400, detail={"message": f"Invalid request: {str(e)}"})
        except Exception as e:
            raise HTTPException(status_code=500, detail={"message": f"Cognito password confirmation failed: {str(e)}"})
            
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail={"message": f"Error confirming forgot password: {str(e)}"})


def change_admin_password(access_token: str, new_password: str, db: Session):
    """
    Change admin password using custom JWT token with database tracking
    """
    try:
        # Validate custom JWT token and extract username
        payload = validate_password_change_token(access_token)
        if not payload:
            raise HTTPException(status_code=401, detail={"message": "Link is expired"})
        
        username = payload.get("username")
        if not username:
            raise HTTPException(status_code=400, detail={"message": "Invalid token: missing username"})
        
        # Check if token has already been used
        token_hash = get_token_hash(access_token)
        used_token = db.query(UsedPasswordTokenModel).filter(UsedPasswordTokenModel.token_hash == token_hash).first()
        if used_token:
            raise HTTPException(status_code=400, detail={"message": "You already changed the password"})
        
        # Check if admin exists in database
        admin = db.query(AdminModel).filter(AdminModel.username == username).first()
        if not admin:
            raise HTTPException(status_code=404, detail={"message": "Admin not found"})
        
        # Check if admin has temporary password
        if not getattr(admin, "isTempPassword"):
            raise HTTPException(status_code=400, detail={"message": "Password has already been changed"})
        
        try:
            # Update password in Cognito
            client.admin_set_user_password(
                UserPoolId=settings.COGNITO_USER_POOL_ID,
                Username=username,
                Password=new_password,
                Permanent=True
            )
            
            # Update database to reflect password change
            setattr(admin, "isTempPassword", False)
            setattr(admin, "updatedBy", admin.cognitoId)  # Self-updated
            setattr(admin, "dateUpdated", datetime.datetime.now(datetime.timezone.utc))
            
            # Store used token in database to prevent reuse
            token_expiration = get_token_expiration(access_token)
            used_token_record = UsedPasswordTokenModel(
                token_hash=token_hash,
                username=username,
                expires_at=token_expiration
            )
            db.add(used_token_record)
            
            db.commit()
            
            return success_response(
                "Password changed successfully. You can now login with your new password.",
                {
                    "username": username,
                    "passwordChanged": True,
                    "isTempPassword": False
                }
            )
            
        except client.exceptions.UserNotFoundException:
            raise HTTPException(status_code=404, detail={"message": "Admin not found in Cognito"})
        except client.exceptions.InvalidPasswordException as e:
            raise HTTPException(status_code=400, detail={"message": f"Invalid password: {str(e)}"})
        except Exception as e:
            raise HTTPException(status_code=500, detail={"message": f"Cognito password update failed: {str(e)}"})
            
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail={"message": f"Error changing password: {str(e)}"})
    