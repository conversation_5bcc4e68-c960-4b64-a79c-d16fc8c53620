from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from uuid import UUID

# ========================
# Feature Flag Schemas
# ========================

class FeatureFlagBase(BaseModel):
    memberId: int = Field(..., description="Member ID")
    featureHandle: str = Field(..., description="Feature handle/name")
    enabled: bool = Field(default=False, description="Whether the feature is enabled")

class FeatureFlagCreate(FeatureFlagBase):
    createdBy: Optional[str] = Field(None, description="Admin UUID who created the feature flag")

class FeatureFlagUpdate(BaseModel):
    featureHandle: Optional[str] = Field(None, description="Feature handle/name")
    enabled: Optional[bool] = Field(None, description="Whether the feature is enabled")
    updatedBy: Optional[str] = Field(None, description="Admin UUID who updated the feature flag")

class FeatureFlagResponse(FeatureFlagBase):
    uuid: str = Field(..., description="Feature flag UUID")
    createdBy: Optional[str] = None
    updatedBy: Optional[str] = None
    dateCreated: datetime
    dateUpdated: Optional[datetime] = None

    class Config:
        from_attributes = True

class FeatureFlagListResponse(BaseModel):
    statusCode: int
    success: bool
    message: str
    featureFlags: List[FeatureFlagResponse]

# ========================
# Bookmark Schemas
# ========================

class BookmarkBase(BaseModel):
    memberId: int = Field(..., description="Member ID")
    entryId: int = Field(..., description="Entry ID to bookmark")

class BookmarkCreate(BookmarkBase):
    createdBy: Optional[str] = Field(None, description="Admin UUID who created the bookmark")

class BookmarkUpdate(BaseModel):
    entryId: Optional[int] = Field(None, description="Entry ID to bookmark")
    updatedBy: Optional[str] = Field(None, description="Admin UUID who updated the bookmark")

class BookmarkResponse(BookmarkBase):
    uuid: str = Field(..., description="Bookmark UUID")
    dateCreated: datetime
    createdBy: Optional[str] = None
    updatedBy: Optional[str] = None
    dateUpdated: Optional[datetime] = None

    class Config:
        from_attributes = True

class BookmarkListResponse(BaseModel):
    statusCode: int
    success: bool
    message: str
    bookmarks: List[BookmarkResponse]

# ========================
# Member Relationship Schemas
# ========================

class MemberWithFeatureFlags(BaseModel):
    uuid: str = Field(..., description="Member UUID")
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    loginEmail: Optional[str] = None
    featureFlags: List[FeatureFlagResponse] = []

    class Config:
        from_attributes = True

class MemberWithBookmarks(BaseModel):
    uuid: str = Field(..., description="Member UUID")
    firstName: Optional[str] = None
    lastName: Optional[str] = None
    loginEmail: Optional[str] = None
    bookmarks: List[BookmarkResponse] = []

    class Config:
        from_attributes = True

# ========================
# Bulk Operation Schemas
# ========================

class BulkFeatureFlagCreate(BaseModel):
    featureFlags: List[FeatureFlagCreate]

class BulkBookmarkCreate(BaseModel):
    bookmarks: List[BookmarkCreate]

# ========================
# Search Schemas
# ========================

class FeatureFlagSearchResponse(BaseModel):
    statusCode: int
    success: bool
    message: str
    featureFlags: List[FeatureFlagResponse]
    totalCount: int
    page: int
    pageSize: int

class BookmarkSearchResponse(BaseModel):
    statusCode: int
    success: bool
    message: str
    bookmarks: List[BookmarkResponse]
    totalCount: int
    page: int
    pageSize: int 