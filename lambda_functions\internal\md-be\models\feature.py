from sqlalchemy import Column, Integer, String, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, func, text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from db.db import Base
import uuid

class CoFeatureFlag(Base):
    __tablename__ = 'co_feature_flags'

    id = Column("id", Integer, primary_key=True, index=True)
    uuid = Column("uuid", UUID(as_uuid=True), unique=True, default=uuid.uuid4, nullable=False)
    memberId = Column("memberId", Integer, ForeignKey('co_members.id', ondelete="CASCADE"), nullable=False)
    featureHandle = Column("featureHandle", String(255), nullable=False)
    enabled = Column("enabled", Boolean, default=False, nullable=False)
    createdBy = Column("createdBy", String(255), nullable=True)
    updatedBy = Column("updatedBy", String(255), nullable=True)
    dateCreated = Column("dateCreated", DateTime, server_default=func.now())
    dateUpdated = Column("dateUpdated", DateTime, onupdate=func.now())

    member = relationship("CoMember", back_populates="feature_flags")

    def __repr__(self):
        return f"<CoFeatureFlag id={self.id} uuid={self.uuid} memberId={self.memberId} featureHandle={self.featureHandle} enabled={self.enabled}>"


class CoMemberBookmark(Base):
    __tablename__ = 'co_member_bookmarks'

    id = Column("id", Integer, primary_key=True, index=True)
    uuid = Column("uuid", UUID(as_uuid=True), unique=True, default=uuid.uuid4, nullable=False)
    entryId = Column("entryId", Integer, nullable=False)
    memberId = Column("memberId", Integer, ForeignKey('co_members.id', ondelete="CASCADE"), nullable=False)
    dateCreated = Column("dateCreated", DateTime, server_default=func.now(), nullable=False)
    createdBy = Column("createdBy", String(255), nullable=True)
    updatedBy = Column("updatedBy", String(255), nullable=True)
    dateUpdated = Column("dateUpdated", DateTime, onupdate=func.now())

    member = relationship("CoMember", back_populates="bookmarks")

    def __repr__(self):
        return f"<CoMemberBookmark id={self.id} uuid={self.uuid} memberId={self.memberId} entryId={self.entryId}>" 