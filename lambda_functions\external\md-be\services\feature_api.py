from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from models.feature import CoFeatureFlag, CoMemberBookmark
from models.member import CoMember
from schemas.feature import *
from typing import List, Optional, Dict, Any
from fastapi import HTTPException, status
import logging

logger = logging.getLogger(__name__)

# ========================
# Feature Flag Services
# ========================

def create_feature_flag(feature_flag: FeatureFlagCreate, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
    """Create a new feature flag"""
    try:
        # Check if member exists
        member = db.query(CoMember).filter(CoMember.id == feature_flag.memberId).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Member with ID {feature_flag.memberId} not found"
            )

        # Check if feature flag already exists for this member and feature handle
        existing_flag = db.query(CoFeatureFlag).filter(
            and_(
                CoFeatureFlag.memberId == feature_flag.memberId,
                CoFeatureFlag.featureHandle == feature_flag.featureHandle
            )
        ).first()
        
        if existing_flag:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Feature flag '{feature_flag.featureHandle}' already exists for member {feature_flag.memberId}"
            )

        # Create new feature flag
        db_feature_flag = CoFeatureFlag(
            memberId=feature_flag.memberId,
            featureHandle=feature_flag.featureHandle,
            enabled=feature_flag.enabled,
            createdBy=user.get('sub', feature_flag.createdBy)
        )
        
        db.add(db_feature_flag)
        db.commit()
        db.refresh(db_feature_flag)
        
        feature_flag_dict = {
            "uuid": str(db_feature_flag.uuid),
            "memberId": db_feature_flag.memberId,
            "featureHandle": db_feature_flag.featureHandle,
            "enabled": db_feature_flag.enabled,
            "createdBy": db_feature_flag.createdBy,
            "updatedBy": db_feature_flag.updatedBy,
            "dateCreated": db_feature_flag.dateCreated,
            "dateUpdated": db_feature_flag.dateUpdated
        }
        
        return {
            "statusCode": status.HTTP_201_CREATED,
            "success": True,
            "message": "Feature flag created successfully",
            "featureFlag": FeatureFlagResponse.model_validate(feature_flag_dict)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating feature flag: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating feature flag: {str(e)}"
        )

def get_all_feature_flags(db: Session, perPage: int = 1, pageSize: int = 10) -> Dict[str, Any]:
    """Get all feature flags with pagination"""
    try:
        offset = (perPage - 1) * pageSize
        
        feature_flags = db.query(CoFeatureFlag).offset(offset).limit(pageSize).all()
        total_count = db.query(CoFeatureFlag).count()
        
        feature_flags_list = []
        for flag in feature_flags:
            flag_dict = {
                "uuid": str(flag.uuid),
                "memberId": flag.memberId,
                "featureHandle": flag.featureHandle,
                "enabled": flag.enabled,
                "createdBy": flag.createdBy,
                "updatedBy": flag.updatedBy,
                "dateCreated": flag.dateCreated,
                "dateUpdated": flag.dateUpdated
            }
            feature_flags_list.append(FeatureFlagResponse.model_validate(flag_dict))
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": "Feature flags retrieved successfully",
            "featureFlags": feature_flags_list,
            "totalCount": total_count,
            "page": perPage,
            "pageSize": pageSize
        }
        
    except Exception as e:
        logger.error(f"Error retrieving feature flags: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving feature flags: {str(e)}"
        )

def get_feature_flag_by_uuid(feature_flag_uuid: str, db: Session) -> Dict[str, Any]:
    """Get feature flag by UUID"""
    try:
        feature_flag = db.query(CoFeatureFlag).filter(CoFeatureFlag.uuid == feature_flag_uuid).first()
        
        if not feature_flag:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Feature flag with UUID {feature_flag_uuid} not found"
            )
        
        feature_flag_dict = {
            "uuid": str(feature_flag.uuid),
            "memberId": feature_flag.memberId,
            "featureHandle": feature_flag.featureHandle,
            "enabled": feature_flag.enabled,
            "createdBy": feature_flag.createdBy,
            "updatedBy": feature_flag.updatedBy,
            "dateCreated": feature_flag.dateCreated,
            "dateUpdated": feature_flag.dateUpdated
        }
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": "Feature flag retrieved successfully",
            "featureFlag": FeatureFlagResponse.model_validate(feature_flag_dict)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving feature flag: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving feature flag: {str(e)}"
        )

def update_feature_flag_by_uuid(feature_flag_uuid: str, feature_flag: FeatureFlagUpdate, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
    """Update feature flag by UUID"""
    try:
        db_feature_flag = db.query(CoFeatureFlag).filter(CoFeatureFlag.uuid == feature_flag_uuid).first()
        
        if not db_feature_flag:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Feature flag with UUID {feature_flag_uuid} not found"
            )
        
        # Update fields
        if feature_flag.featureHandle is not None:
            db_feature_flag.featureHandle = feature_flag.featureHandle
        if feature_flag.enabled is not None:
            db_feature_flag.enabled = feature_flag.enabled
        
        db_feature_flag.updatedBy = user.get('sub', feature_flag.updatedBy)
        
        db.commit()
        db.refresh(db_feature_flag)
        
        feature_flag_dict = {
            "uuid": str(db_feature_flag.uuid),
            "memberId": db_feature_flag.memberId,
            "featureHandle": db_feature_flag.featureHandle,
            "enabled": db_feature_flag.enabled,
            "createdBy": db_feature_flag.createdBy,
            "updatedBy": db_feature_flag.updatedBy,
            "dateCreated": db_feature_flag.dateCreated,
            "dateUpdated": db_feature_flag.dateUpdated
        }
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": "Feature flag updated successfully",
            "featureFlag": FeatureFlagResponse.model_validate(feature_flag_dict)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating feature flag: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating feature flag: {str(e)}"
        )

def delete_feature_flag_by_uuid(feature_flag_uuid: str, db: Session) -> Dict[str, Any]:
    """Delete feature flag by UUID"""
    try:
        feature_flag = db.query(CoFeatureFlag).filter(CoFeatureFlag.uuid == feature_flag_uuid).first()
        
        if not feature_flag:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Feature flag with UUID {feature_flag_uuid} not found"
            )
        
        db.delete(feature_flag)
        db.commit()
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": f"Feature flag '{feature_flag.featureHandle}' deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting feature flag: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting feature flag: {str(e)}"
        )

def get_feature_flags_by_member(member_id: int, db: Session) -> Dict[str, Any]:
    """Get all feature flags for a specific member"""
    try:
        # Check if member exists
        member = db.query(CoMember).filter(CoMember.id == member_id).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Member with ID {member_id} not found"
            )
        
        feature_flags = db.query(CoFeatureFlag).filter(CoFeatureFlag.memberId == member_id).all()
        
        feature_flags_list = []
        for flag in feature_flags:
            flag_dict = {
                "uuid": str(flag.uuid),
                "memberId": flag.memberId,
                "featureHandle": flag.featureHandle,
                "enabled": flag.enabled,
                "createdBy": flag.createdBy,
                "updatedBy": flag.updatedBy,
                "dateCreated": flag.dateCreated,
                "dateUpdated": flag.dateUpdated
            }
            feature_flags_list.append(FeatureFlagResponse.model_validate(flag_dict))
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": f"Feature flags for member {member_id} retrieved successfully",
            "featureFlags": feature_flags_list
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving feature flags by member: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving feature flags by member: {str(e)}"
        )

def search_feature_flags(query: str, db: Session, perPage: int = 1, pageSize: int = 10) -> Dict[str, Any]:
    """Search feature flags by feature handle or member ID"""
    try:
        offset = (perPage - 1) * pageSize
        
        # Search by feature handle or member ID
        feature_flags = db.query(CoFeatureFlag).filter(
            or_(
                CoFeatureFlag.featureHandle.ilike(f"%{query}%"),
                CoFeatureFlag.memberId == query if query.isdigit() else False
            )
        ).offset(offset).limit(pageSize).all()
        
        total_count = db.query(CoFeatureFlag).filter(
            or_(
                CoFeatureFlag.featureHandle.ilike(f"%{query}%"),
                CoFeatureFlag.memberId == query if query.isdigit() else False
            )
        ).count()
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": "Feature flags search completed successfully",
            "featureFlags": feature_flags,
            "totalCount": total_count,
            "page": perPage,
            "pageSize": pageSize
        }
        
    except Exception as e:
        logger.error(f"Error searching feature flags: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching feature flags: {str(e)}"
        )

def bulk_create_feature_flags(feature_flags: List[FeatureFlagCreate], user: Dict[str, Any], db: Session) -> Dict[str, Any]:
    """Bulk create feature flags"""
    try:
        created_flags = []
        
        for feature_flag in feature_flags:
            # Check if member exists
            member = db.query(CoMember).filter(CoMember.id == feature_flag.memberId).first()
            if not member:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Member with ID {feature_flag.memberId} not found"
                )
            
            # Check if feature flag already exists
            existing_flag = db.query(CoFeatureFlag).filter(
                and_(
                    CoFeatureFlag.memberId == feature_flag.memberId,
                    CoFeatureFlag.featureHandle == feature_flag.featureHandle
                )
            ).first()
            
            if existing_flag:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Feature flag '{feature_flag.featureHandle}' already exists for member {feature_flag.memberId}"
                )
            
            # Create feature flag
            db_feature_flag = CoFeatureFlag(
                memberId=feature_flag.memberId,
                featureHandle=feature_flag.featureHandle,
                enabled=feature_flag.enabled,
                createdBy=user.get('sub', feature_flag.createdBy)
            )
            
            db.add(db_feature_flag)
            created_flags.append(db_feature_flag)
        
        db.commit()
        
        # Refresh all created flags to get their UUIDs
        for flag in created_flags:
            db.refresh(flag)
        
        return {
            "statusCode": status.HTTP_201_CREATED,
            "success": True,
            "message": f"{len(created_flags)} feature flags created successfully",
            "featureFlags": created_flags
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error bulk creating feature flags: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error bulk creating feature flags: {str(e)}"
        )

# ========================
# Bookmark Services
# ========================

def create_bookmark(bookmark: BookmarkCreate, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
    """Create a new bookmark"""
    try:
        # Check if member exists
        member = db.query(CoMember).filter(CoMember.id == bookmark.memberId).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Member with ID {bookmark.memberId} not found"
            )

        # Check if bookmark already exists for this member and entry
        existing_bookmark = db.query(CoMemberBookmark).filter(
            and_(
                CoMemberBookmark.memberId == bookmark.memberId,
                CoMemberBookmark.entryId == bookmark.entryId
            )
        ).first()
        
        if existing_bookmark:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Bookmark already exists for member {bookmark.memberId} and entry {bookmark.entryId}"
            )

        # Create new bookmark
        db_bookmark = CoMemberBookmark(
            memberId=bookmark.memberId,
            entryId=bookmark.entryId,
            createdBy=user.get('sub', bookmark.createdBy)
        )
        
        db.add(db_bookmark)
        db.commit()
        db.refresh(db_bookmark)
        
        bookmark_dict = {
            "uuid": str(db_bookmark.uuid),
            "memberId": db_bookmark.memberId,
            "entryId": db_bookmark.entryId,
            "dateCreated": db_bookmark.dateCreated,
            "createdBy": db_bookmark.createdBy,
            "updatedBy": db_bookmark.updatedBy,
            "dateUpdated": db_bookmark.dateUpdated
        }
        
        return {
            "statusCode": status.HTTP_201_CREATED,
            "success": True,
            "message": "Bookmark created successfully",
            "bookmark": BookmarkResponse.model_validate(bookmark_dict)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating bookmark: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating bookmark: {str(e)}"
        )

def get_all_bookmarks(db: Session, perPage: int = 1, pageSize: int = 10) -> Dict[str, Any]:
    """Get all bookmarks with pagination"""
    try:
        offset = (perPage - 1) * pageSize
        
        bookmarks = db.query(CoMemberBookmark).offset(offset).limit(pageSize).all()
        total_count = db.query(CoMemberBookmark).count()
        
        bookmarks_list = []
        for bookmark in bookmarks:
            bookmark_dict = {
                "uuid": str(bookmark.uuid),
                "memberId": bookmark.memberId,
                "entryId": bookmark.entryId,
                "dateCreated": bookmark.dateCreated,
                "createdBy": bookmark.createdBy,
                "updatedBy": bookmark.updatedBy,
                "dateUpdated": bookmark.dateUpdated
            }
            bookmarks_list.append(BookmarkResponse.model_validate(bookmark_dict))
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": "Bookmarks retrieved successfully",
            "bookmarks": bookmarks_list,
            "totalCount": total_count,
            "page": perPage,
            "pageSize": pageSize
        }
        
    except Exception as e:
        logger.error(f"Error retrieving bookmarks: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving bookmarks: {str(e)}"
        )

def get_bookmark_by_uuid(bookmark_uuid: str, db: Session) -> Dict[str, Any]:
    """Get bookmark by UUID"""
    try:
        bookmark = db.query(CoMemberBookmark).filter(CoMemberBookmark.uuid == bookmark_uuid).first()
        
        if not bookmark:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Bookmark with UUID {bookmark_uuid} not found"
            )
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": "Bookmark retrieved successfully",
            "bookmark": bookmark
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving bookmark: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving bookmark: {str(e)}"
        )

def update_bookmark_by_uuid(bookmark_uuid: str, bookmark: BookmarkUpdate, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
    """Update bookmark by UUID"""
    try:
        db_bookmark = db.query(CoMemberBookmark).filter(CoMemberBookmark.uuid == bookmark_uuid).first()
        
        if not db_bookmark:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Bookmark with UUID {bookmark_uuid} not found"
            )
        
        # Update fields
        if bookmark.entryId is not None:
            db_bookmark.entryId = bookmark.entryId
        
        db_bookmark.updatedBy = user.get('sub', bookmark.updatedBy)
        
        db.commit()
        db.refresh(db_bookmark)
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": "Bookmark updated successfully",
            "bookmark": db_bookmark
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating bookmark: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating bookmark: {str(e)}"
        )

def delete_bookmark_by_uuid(bookmark_uuid: str, user: Dict[str, Any], db: Session) -> Dict[str, Any]:
    """Delete bookmark by UUID"""
    try:
        db_bookmark = db.query(CoMemberBookmark).filter(CoMemberBookmark.uuid == bookmark_uuid).first()
        
        if not db_bookmark:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Bookmark with UUID {bookmark_uuid} not found"
            )
        
        entry_id = db_bookmark.entryId
        member_id = db_bookmark.memberId
        
        db.delete(db_bookmark)
        db.commit()
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": f"Bookmark for entry {entry_id} of member {member_id} deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting bookmark: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting bookmark: {str(e)}"
        )

def get_bookmarks_by_member(member_id: int, db: Session) -> Dict[str, Any]:
    """Get all bookmarks for a specific member"""
    try:
        # Check if member exists
        member = db.query(CoMember).filter(CoMember.id == member_id).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Member with ID {member_id} not found"
            )
        
        bookmarks = db.query(CoMemberBookmark).filter(CoMemberBookmark.memberId == member_id).all()
        
        bookmarks_list = []
        for bookmark in bookmarks:
            bookmark_dict = {
                "uuid": str(bookmark.uuid),
                "memberId": bookmark.memberId,
                "entryId": bookmark.entryId,
                "dateCreated": bookmark.dateCreated,
                "createdBy": bookmark.createdBy,
                "updatedBy": bookmark.updatedBy,
                "dateUpdated": bookmark.dateUpdated
            }
            bookmarks_list.append(BookmarkResponse.model_validate(bookmark_dict))
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": f"Bookmarks for member {member_id} retrieved successfully",
            "bookmarks": bookmarks_list
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving bookmarks by member: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving bookmarks by member: {str(e)}"
        )

def search_bookmarks(query: str, db: Session, perPage: int = 1, pageSize: int = 10) -> Dict[str, Any]:
    """Search bookmarks by member ID or entry ID"""
    try:
        offset = (perPage - 1) * pageSize
        
        # Search by member ID or entry ID
        bookmarks = db.query(CoMemberBookmark).filter(
            or_(
                CoMemberBookmark.memberId == query if query.isdigit() else False,
                CoMemberBookmark.entryId == query if query.isdigit() else False
            )
        ).offset(offset).limit(pageSize).all()
        
        total_count = db.query(CoMemberBookmark).filter(
            or_(
                CoMemberBookmark.memberId == query if query.isdigit() else False,
                CoMemberBookmark.entryId == query if query.isdigit() else False
            )
        ).count()
        
        return {
            "statusCode": status.HTTP_200_OK,
            "success": True,
            "message": "Bookmarks search completed successfully",
            "bookmarks": bookmarks,
            "totalCount": total_count,
            "page": perPage,
            "pageSize": pageSize
        }
        
    except Exception as e:
        logger.error(f"Error searching bookmarks: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching bookmarks: {str(e)}"
        )

def bulk_create_bookmarks(bookmarks: List[BookmarkCreate], user: Dict[str, Any], db: Session) -> Dict[str, Any]:
    """Bulk create bookmarks"""
    try:
        created_bookmarks = []
        
        for bookmark in bookmarks:
            # Check if member exists
            member = db.query(CoMember).filter(CoMember.id == bookmark.memberId).first()
            if not member:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Member with ID {bookmark.memberId} not found"
                )
            
            # Check if bookmark already exists
            existing_bookmark = db.query(CoMemberBookmark).filter(
                and_(
                    CoMemberBookmark.memberId == bookmark.memberId,
                    CoMemberBookmark.entryId == bookmark.entryId
                )
            ).first()
            
            if existing_bookmark:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Bookmark for entry {bookmark.entryId} already exists for member {bookmark.memberId}"
                )
            
            # Create bookmark
            db_bookmark = CoMemberBookmark(
                memberId=bookmark.memberId,
                entryId=bookmark.entryId,
                createdBy=user.get('sub', bookmark.createdBy)
            )
            
            db.add(db_bookmark)
            created_bookmarks.append(db_bookmark)
        
        db.commit()
        
        # Refresh all created bookmarks to get their UUIDs
        for bookmark in created_bookmarks:
            db.refresh(bookmark)
        
        return {
            "statusCode": status.HTTP_201_CREATED,
            "success": True,
            "message": f"{len(created_bookmarks)} bookmarks created successfully",
            "bookmarks": created_bookmarks
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error bulk creating bookmarks: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error bulk creating bookmarks: {str(e)}"
        ) 