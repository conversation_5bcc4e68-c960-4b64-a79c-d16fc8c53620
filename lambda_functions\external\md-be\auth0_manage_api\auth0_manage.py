import requests
from config import settings

def get_management_token():
    url = f"{settings.ISSUER}/oauth/token"
    payload = {
        "client_id": settings.CLIENT_ID,
        "client_secret": settings.CLIENT_SECRET,
        "audience": settings.AUDIENCE,
        "grant_type": "client_credentials"
    }

    response = requests.post(url, json=payload)
    data = response.json()

    if "access_token" not in data:
        raise Exception(f"Auth0 token error: {data}")

    return data["access_token"]

