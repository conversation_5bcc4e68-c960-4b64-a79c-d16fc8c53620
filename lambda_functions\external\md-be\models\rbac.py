from sqlalchemy import Column, Integer, String, Boolean, ForeignKey, DateTime, UniqueConstraint, func, text, Text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from db.db import Base

class Role(Base):
    __tablename__ = 'co_roles'

    id = Column("id", Integer, primary_key=True, index=True)
    name = Column("name", String(100), unique=True, nullable=False)
    slug = Column("slug", String(255))
    uuid = Column("uuid", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), unique=True, nullable=False)
    description = Column("description", Text, nullable=True)
    createdBy = Column("createdBy", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), nullable=False)
    updatedBy = Column("updatedBy", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), nullable=False)
    dateCreated = Column("dateCreated", DateTime, default=func.now())
    dateUpdated = Column("dateUpdated", DateTime, default=func.now(), onupdate=func.now())
    permissions = relationship("RoleModulePermission", back_populates="role")

class Module(Base):
    __tablename__ = 'co_modules'

    id = Column("id", Integer, primary_key=True, index=True)
    name = Column("name", String(100), unique=True, nullable=False)
    slug = Column("slug", String(255))
    uuid = Column("uuid", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), unique=True, nullable=False)
    createdBy = Column("createdBy", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), nullable=False)
    updatedBy = Column("updatedBy", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), nullable=False)
    dateCreated = Column("dateCreated", DateTime, default=func.now())
    dateUpdated = Column("dateUpdated", DateTime, default=func.now(), onupdate=func.now())
    permissions = relationship("RoleModulePermission", back_populates="module")

class RoleModulePermission(Base):
    __tablename__ = 'co_role_module_permissions'
    __table_args__ = (
        UniqueConstraint('roleId', 'moduleId', name='uq_role_module_perm'),
    )

    id = Column("id", Integer, primary_key=True)
    roleId = Column("roleId", Integer, ForeignKey('co_roles.id', ondelete="CASCADE"))
    moduleId = Column("moduleId", Integer, ForeignKey('co_modules.id', ondelete="CASCADE"))
    create = Column("create", Boolean, default=False)
    update = Column("update", Boolean, default=False)
    delete = Column("delete", Boolean, default=False)
    view = Column("view", Boolean, default=False)
    createdBy = Column("createdBy", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), nullable=False)
    updatedBy = Column("updatedBy", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), nullable=False)
    dateCreated = Column("dateCreated", DateTime, default=func.now())
    dateUpdated = Column("dateUpdated", DateTime, default=func.now(), onupdate=func.now())
    role = relationship("Role", back_populates="permissions")
    module = relationship("Module", back_populates="permissions")



