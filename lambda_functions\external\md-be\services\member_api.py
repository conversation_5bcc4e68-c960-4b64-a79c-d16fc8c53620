from auth0_manage_api.auth0_manage import get_management_token
from auth0.management import Auth0
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from sqlalchemy.inspection import inspect
from sqlalchemy import and_, asc
from models.member import CoMember, CoAuth0User
from models.member_verification import MemberVerification
from models.admin import AdminModel
from models.organization import CoOrganization, CoRelationsMembersOrganizations
from schemas.member import (
    CoMemberCreate, CoMemberUpdate, CoMemberResponse, CoMemberBulkUpsert,
    BulkUpsertMembersRequest, BulkUpsertMembersResponse, MemberOperationResult,
    CoMemberWithOrganizations, OrganizationInfo, BulkDeleteMembersRequest,
    BulkDeleteMembersResponse, MemberDeleteResult
)
from typing import List, Optional
from config import settings
from utils.response_utils import success_response
from utils.cloudwatch_logger import cloudwatch_logger
import secrets
import string
from uuid import UUID
from models.log import CoLog


def get_total_member_count(db: Session):
    total_count = db.query(CoMember).count()
    return success_response(
        "Total member count retrieved successfully",
        {
            "totalCount": total_count
        }
    )

def get_all_members(db: Session, page: int, pageSize: int, identityType: Optional[str] = None):
    # Get total count
    total_count = db.query(CoMember).count()
    
    # Get paginated results
    filters = []
    if identityType:
        filters.append(CoMember.identityType == identityType)

    members = (
        db.query(CoMember)
        .filter(*filters)
        .order_by(CoMember.firstName.asc())
        .offset((page - 1) * pageSize)
        .limit(pageSize)
        .all()
    )
    
    if not members:
        return success_response(
            "No members found",
            {
                "members": [],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": page,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": page * pageSize < total_count,
                    "hasPrevious": page > 1
                }
            }
        )
    
    return success_response(
        "Members retrieved successfully",
        {
            "members": [CoMemberResponse.model_validate(member) for member in members],
            "pagination": {
                "totalCount": total_count,
                "currentPage": page,
                "pageSize": pageSize,
                "totalPages": (total_count + pageSize - 1) // pageSize,
                "hasNext": page * pageSize < total_count,
                "hasPrevious": page > 1
            }
        }
    )

def get_member_by_auth0id(auth0id: str, db: Session):
    member = db.query(CoMember).filter_by(auth0Id=auth0id).first()
    if not member:
        raise HTTPException(status_code=404, detail="No member found")
    verification = db.query(MemberVerification).filter_by(member_uuid=member.uuid).first()
    if verification:
        member.verificationStatus = verification.verification_status
    return success_response(
        "Member retrieved successfully",
        {"member": CoMemberResponse.model_validate(member)}
    )

def get_member_by_uuid(uuid: str, db: Session):
    try:
        uuid_obj = UUID(uuid)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid UUID format")

    member = db.query(CoMember).filter_by(uuid=uuid_obj).first()
    if not member:
        raise HTTPException(status_code=404, detail="No member found")
    
    verification = db.query(MemberVerification).filter_by(member_uuid=uuid_obj).first()
    if verification:
        member.verificationStatus = verification.verification_status
        
    return success_response(
        "Member retrieved successfully",
        {"member": CoMemberResponse.model_validate(member)},
    )

def update_member_by_uuid(uuid: str, member: CoMemberUpdate, db: Session, admin_user_payload: Optional[dict] = None):
    existing_member = db.query(CoMember).filter_by(uuid=uuid).first()
    if not existing_member:
        raise HTTPException(status_code=404, detail="No member found")
    co_auth0_user = db.query(CoAuth0User).filter_by(userId=existing_member.auth0Id).first()
    
    # Get user ID for logging
    user_id = None
    if admin_user_payload:
        user_id = admin_user_payload.get("user_id") or admin_user_payload.get("sub")
    
    # Log before state
    before_state = {
        "uuid": str(existing_member.uuid),
        "firstName": existing_member.firstName,
        "lastName": existing_member.lastName,
        "loginEmail": existing_member.loginEmail,
        "personalBusinessEmail": existing_member.personalBusinessEmail,
        "membershipTier": existing_member.membershipTier,
        "communityStatus": existing_member.communityStatus,
        "identityType": existing_member.identityType,
        "auth0Id": existing_member.auth0Id,
        "updatedByAdmin": str(existing_member.updatedByAdmin) if existing_member.updatedByAdmin else None,
        "updatedByMember": str(existing_member.updatedByMember) if existing_member.updatedByMember else None,
        "dateUpdated": existing_member.dateUpdated.isoformat() if existing_member.dateUpdated else None
    }
    print(before_state,"before_state")
    cloudwatch_logger.log_api_operation(
        operation="update",
        resource_id=str(uuid),
        resource_type="member",
        before_state=before_state,
        user_id=user_id,
        additional_context={
            "update_data": member.model_dump(exclude_unset=True),
            "auth0_user_id": existing_member.auth0Id
        }
    )
    
    try:
        # Update user in Auth0 only if auth0Id exists (email and password must be updated separately)
        if existing_member.auth0Id is not None:
            domain = settings.DOMAIN
            token = get_management_token()

            if not domain or not token:
                raise HTTPException(
                    status_code=500,
                    detail="Auth0 configuration error: domain or token not available"
                )

            auth0 = Auth0(domain, token)

            try:
                # Update password first if provided
                if member.password:
                    auth0.users.update(
                        id=str(existing_member.auth0Id),
                        body={
                            "password": member.password
                        }
                    )

                # Update email and name second (only if email has changed)
                if member.loginEmail and member.loginEmail != existing_member.loginEmail:
                    updated_user = auth0.users.update(
                        id=str(existing_member.auth0Id),
                        body={
                            "email": member.loginEmail,
                            "name": member.loginEmail
                        }
                    )
                    if updated_user:
                            updated_member_auth0_users = CoAuth0User(
                                userId=updated_user["user_id"],
                                email=updated_user.get("email"),
                                name=updated_user.get("name"),
                                familyName=updated_user.get("familyName"),
                                givenName=updated_user.get("given_name"),
                                nickName=updated_user.get("nickname"),
                                picture=updated_user.get("picture"),
                                emailVerified=updated_user.get("email_verified"),
                                identities=updated_user.get("identities"),
                                createdAt=updated_user.get("created_at"),
                                updatedAt=updated_user.get("updated_at")
                            )
                            
                            # update the co_auth0_user table in database
                            for column in inspect(CoAuth0User).attrs:
                                key = column.key
                                value = getattr(updated_member_auth0_users, key, None)
                                if hasattr(co_auth0_user, key) and value is not None:
                                    setattr(co_auth0_user, key, value)

            except Exception as auth0_error:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to update user in Auth0: {str(auth0_error)}"
                )

            # Update member fields in database
        for key, value in member.model_dump(exclude_unset=True).items():
            if hasattr(existing_member, key):
                setattr(existing_member, key, value)
        
        # Set updated by fields
        if admin_user_payload and admin_user_payload.get("user_type") == "admin":
            admin = db.query(AdminModel).filter_by(cognitoId=admin_user_payload["user_id"]).first()
            if admin:
                existing_member.updatedByAdmin = admin.uuid
        elif(admin_user_payload and admin_user_payload.get("user_type") == "member"):
            existing_member.updatedByMember = existing_member.uuid
            
    except Exception as e:
        # Log error state
        cloudwatch_logger.log_api_operation(
            operation="update",
            resource_id=str(uuid),
            resource_type="member",
            user_id=user_id,
            additional_context={
                "error_type": type(e).__name__,
                "auth0_user_id": existing_member.auth0Id if existing_member else None
            },
            error=str(e)
        )
        raise
        
    db.commit()
    db.refresh(existing_member)
    db.refresh(co_auth0_user)

    # Log after state
    after_state = {
        "uuid": str(existing_member.uuid),
        "firstName": existing_member.firstName,
        "lastName": existing_member.lastName,
        "loginEmail": existing_member.loginEmail,
        "personalBusinessEmail": existing_member.personalBusinessEmail,
        "membershipTier": existing_member.membershipTier,
        "communityStatus": existing_member.communityStatus,
        "identityType": existing_member.identityType,
        "auth0Id": existing_member.auth0Id,
        "updatedByAdmin": str(existing_member.updatedByAdmin) if existing_member.updatedByAdmin else None,
        "updatedByMember": str(existing_member.updatedByMember) if existing_member.updatedByMember else None,
        "dateUpdated": existing_member.dateUpdated.isoformat() if existing_member.dateUpdated else None
    }
    
    cloudwatch_logger.log_api_operation(
        operation="update",
        resource_id=str(uuid),
        resource_type="member",
        after_state=after_state,
        user_id=user_id,
        additional_context={
            "operation_status": "success",
            "auth0_user_id": existing_member.auth0Id
        }
    )

    return success_response(
        "Member updated successfully",
        {"member": CoMemberResponse.model_validate(existing_member)}
    )

def get_member_by_email(email: str, db: Session):
    member = db.query(CoMember).filter_by(loginEmail=email).first()
    if not member:
        raise HTTPException(status_code=404, detail="No member found")
    verification = db.query(MemberVerification).filter_by(member_uuid=member.uuid).first()
    if verification:
        member.verificationStatus = verification.verification_status
    return success_response(
        "Member retrieved successfully",
        {"member": CoMemberResponse.model_validate(member)}
    )

def delete_member(uuid: str, db: Session, admin_user_payload: Optional[dict] = None):
    member = db.query(CoMember).filter_by(uuid=uuid).first()
    if not member:
        raise HTTPException(status_code=404, detail="No member found")

    # Get user ID for logging
    user_id = None
    if admin_user_payload:
        user_id = admin_user_payload.get("user_id") or admin_user_payload.get("sub")
    
    # Log before state
    before_state = {
        "uuid": str(member.uuid),
        "firstName": member.firstName,
        "lastName": member.lastName,
        "loginEmail": member.loginEmail,
        "personalBusinessEmail": member.personalBusinessEmail,
        "membershipTier": member.membershipTier,
        "communityStatus": member.communityStatus,
        "identityType": member.identityType,
        "auth0Id": member.auth0Id,
        "dateCreated": member.dateCreated.isoformat() if member.dateCreated else None
    }
    
    cloudwatch_logger.log_api_operation(
        operation="delete",
        resource_id=str(uuid),
        resource_type="member",
        before_state=before_state,
        user_id=user_id,
        additional_context={
            "auth0_user_id": member.auth0Id
        }
    )

    try:
        domain = settings.DOMAIN
        token = get_management_token()

        if domain and token and member.auth0Id is not None:
            try:
                auth0 = Auth0(domain, token)
                auth0.users.delete(id=str(member.auth0Id))
            except Exception as auth0_error:
                print(f"Warning: Failed to delete user from Auth0: {str(auth0_error)}")

        deleted_member_uuid = getattr(member, 'uuid', None)
        deleted_login_email = getattr(member, 'loginEmail', None)
        deleted_business_email = getattr(member, 'personalBusinessEmail', None)
        deleted_auth0_id = getattr(member, 'auth0Id', None)

        co_auth0_user = db.query(CoAuth0User).filter_by(userId=member.auth0Id).first()
        member_verification = db.query(MemberVerification).filter_by(member_uuid=member.uuid).first()
        
        if co_auth0_user:
            db.delete(co_auth0_user)
        if member_verification:
            db.delete(member_verification)
        db.delete(member)
        db.commit()
        
        # Log successful deletion
        cloudwatch_logger.log_api_operation(
            operation="delete",
            resource_id=str(uuid),
            resource_type="member",
            user_id=user_id,
            additional_context={
                "operation_status": "success",
                "deleted_auth0_id": deleted_auth0_id,
                "deleted_emails": {
                    "login_email": deleted_login_email,
                    "business_email": deleted_business_email
                }
            }
        )
        
    except Exception as e:
        # Log error state
        cloudwatch_logger.log_api_operation(
            operation="delete",
            resource_id=str(uuid),
            resource_type="member",
            user_id=user_id,
            additional_context={
                "error_type": type(e).__name__,
                "auth0_user_id": member.auth0Id
            },
            error=str(e)
        )
        raise

    try:
        actor_cognito_id = None
        if admin_user_payload:
            # Get the Cognito ID from the JWT payload
            actor_cognito_id = admin_user_payload.get("user_id") or admin_user_payload.get("sub")

        context_data = {
            "deleted_member_uuid": str(deleted_member_uuid) if deleted_member_uuid else None,
            "deleted_email": str(deleted_login_email or deleted_business_email) if (deleted_login_email or deleted_business_email) else None,
            "auth0_id": str(deleted_auth0_id) if deleted_auth0_id else None,
        }

        log_entry = CoLog(
            userUuid=actor_cognito_id,
            action="member_delete",
            context=context_data,
            purpose="Member deletion (Privacy opt-out)"
        )
        db.add(log_entry)
        db.commit()
    except Exception as e:
        print(f"Warning: Failed to log member deletion: {str(e)}")
        db.rollback()

    return success_response("Member deleted successfully")

# ========================
# Bulk Operations
# ========================

def generate_random_password(length: int = 12) -> str:
    """Generate a random password with at least one uppercase, one special character, and one number"""
    # Ensure minimum requirements
    uppercase = secrets.choice(string.ascii_uppercase)
    special = secrets.choice("!@#$%^&*")
    number = secrets.choice(string.digits)

    # Fill the rest with random characters
    remaining_length = max(0, length - 3)
    remaining_chars = ''.join(secrets.choice(string.ascii_letters + string.digits + "!@#$%^&*")
                             for _ in range(remaining_length))

    # Combine and shuffle
    password_chars = list(uppercase + special + number + remaining_chars)
    secrets.SystemRandom().shuffle(password_chars)

    return ''.join(password_chars)

def bulk_upsert_members(request: BulkUpsertMembersRequest, db: Session, admin_user_payload: Optional[dict] = None):
    """
    Bulk upsert members using loginEmail as unique identifier.
    Best-effort processing with detailed error reporting.
    Optimized to reduce database queries by batching operations.
    """
    # Get user ID for logging
    user_id = None
    if admin_user_payload:
        user_id = admin_user_payload.get("user_id") or admin_user_payload.get("sub")
    
    # Log bulk operation start
    cloudwatch_logger.log_api_operation(
        operation="bulk_upsert",
        resource_id="bulk_operation",
        resource_type="member",
        user_id=user_id,
        additional_context={
            "total_members": len(request.members),
            "member_emails": [str(member.loginEmail) for member in request.members],
            "operation_type": "bulk_upsert"
        }
    )
    
    results = []
    successful_count = 0
    failed_count = 0

    # Get Auth0 management token once for all operations
    domain = settings.DOMAIN
    management_token = None
    auth0_client = None

    if domain:
        try:
            management_token = get_management_token()
            if management_token:
                auth0_client = Auth0(domain, management_token)
        except Exception as e:
            print(f"Warning: Failed to initialize Auth0 client: {str(e)}")
    
    # Step 1: Collect all login emails for efficient querying (convert EmailStr to str)
    login_emails = [str(member.loginEmail) for member in request.members]

    # Step 2: Fetch all existing members in a single query
    existing_members_query = db.query(CoMember).filter(CoMember.loginEmail.in_(login_emails)).all()

    # Step 3: Create a map for quick lookup (ensure consistent string keys)
    existing_members_map = {str(member.loginEmail): member for member in existing_members_query}
    
    # Process each member with optimized database access
    for member_data in request.members:
        try:
            # Check if member exists using our pre-populated map
            existing_member = existing_members_map.get(str(member_data.loginEmail))

            if existing_member:
                # Update existing member
                try:
                    # Log before state for update
                    before_state = {
                        "uuid": str(existing_member.uuid),
                        "firstName": existing_member.firstName,
                        "lastName": existing_member.lastName,
                        "loginEmail": existing_member.loginEmail,
                        "membershipTier": existing_member.membershipTier,
                        "communityStatus": existing_member.communityStatus,
                        "auth0Id": existing_member.auth0Id
                    }
                    
                    cloudwatch_logger.log_api_operation(
                        operation="bulk_update",
                        resource_id=str(existing_member.uuid),
                        resource_type="member",
                        before_state=before_state,
                        user_id=user_id,
                        additional_context={
                            "bulk_operation": True,
                            "update_data": member_data.model_dump(exclude_unset=True, exclude={'loginEmail'})
                        }
                    )
                    
                    # Update member fields
                    update_data = member_data.model_dump(exclude_unset=True, exclude={'loginEmail'})

                    # Remove password from update if provided (passwords are handled separately)
                    if 'password' in update_data:
                        password = update_data.pop('password')

                        # Update password in Auth0 if provided
                        if password and auth0_client and existing_member.auth0Id is not None:
                            try:
                                auth0_client.users.update(str(existing_member.auth0Id), {"password": password})
                            except Exception as auth0_error:
                                print(f"Warning: Failed to update password in Auth0 for {member_data.loginEmail}: {str(auth0_error)}")

                    # Update database fields
                    for key, value in update_data.items():
                        if hasattr(existing_member, key) and value is not None:
                            setattr(existing_member, key, value)

                    # Set updated by fields
                    if admin_user_payload and admin_user_payload.get("user_type") == "admin":
                        admin = db.query(AdminModel).filter_by(cognitoId=admin_user_payload["user_id"]).first()
                        if admin:
                            existing_member.updatedByAdmin = admin.uuid
                    elif admin_user_payload and admin_user_payload.get("user_type") == "member":
                        existing_member.updatedByMember = existing_member.uuid

                    # Note: We'll commit all updates in a batch later

                    # Commit the update to ensure UUID is available
                    db.commit()
                    db.refresh(existing_member)

                    # Log after state for successful update
                    after_state = {
                        "uuid": str(existing_member.uuid),
                        "firstName": existing_member.firstName,
                        "lastName": existing_member.lastName,
                        "loginEmail": existing_member.loginEmail,
                        "membershipTier": existing_member.membershipTier,
                        "communityStatus": existing_member.communityStatus,
                        "auth0Id": existing_member.auth0Id,
                        "dateUpdated": existing_member.dateUpdated.isoformat() if existing_member.dateUpdated else None
                    }
                    
                    cloudwatch_logger.log_api_operation(
                        operation="bulk_update",
                        resource_id=str(existing_member.uuid),
                        resource_type="member",
                        after_state=after_state,
                        user_id=user_id,
                        additional_context={
                            "bulk_operation": True,
                            "operation_status": "success"
                        }
                    )

                    results.append(MemberOperationResult(
                        loginEmail=str(member_data.loginEmail),
                        success=True,
                        action="updated",
                        message="Member updated successfully",
                        memberUuid=getattr(existing_member, 'uuid')  # Use getattr to access UUID value
                    ))
                    successful_count += 1

                except Exception as e:
                    db.rollback()
                    results.append(MemberOperationResult(
                        loginEmail=str(member_data.loginEmail),
                        success=False,
                        action="failed",
                        message=f"Failed to update member: {str(e)}",
                        memberUuid=None
                    ))
                    failed_count += 1
            else:
                # Create new member
                try:
                    # Check if password is provided for new member
                    if not member_data.password:
                        results.append(MemberOperationResult(
                            loginEmail=str(member_data.loginEmail),
                            success=False,
                            action="failed",
                            message="Password is required for new member creation",
                            memberUuid=None
                        ))
                        failed_count += 1
                        continue

                    # Create user in Auth0 first (required since auth0Id is not nullable)
                    auth0_user_id = None
                    if auth0_client:
                        try:
                            auth0_user = auth0_client.users.create({
                                "email": member_data.loginEmail,
                                "password": member_data.password,
                                "connection": "Username-Password-Authentication",
                                "email_verified": member_data.loginEmailVerified or True
                            })
                            auth0_user_id = auth0_user["user_id"]
                        except Exception as auth0_error:
                            results.append(MemberOperationResult(
                                loginEmail=str(member_data.loginEmail),
                                success=False,
                                action="failed",
                                message=f"Failed to create user in Auth0: {str(auth0_error)}",
                                memberUuid=None
                            ))
                            failed_count += 1
                            continue
                    else:
                        # Auth0 client not available, cannot create member since auth0Id is required
                        results.append(MemberOperationResult(
                            loginEmail=str(member_data.loginEmail),
                            success=False,
                            action="failed",
                            message="Auth0 client not available, cannot create new member",
                            memberUuid=None
                        ))
                        failed_count += 1
                        continue

                    # Create member in database
                    new_member_data = member_data.model_dump(exclude={'password'})
                    new_member = CoMember(
                        auth0Id=auth0_user_id,
                        **new_member_data
                    )
                    
                    # Log new member creation
                    cloudwatch_logger.log_api_operation(
                        operation="bulk_create",
                        resource_id="new_member",
                        resource_type="member",
                        user_id=user_id,
                        additional_context={
                            "bulk_operation": True,
                            "member_data": new_member_data,
                            "auth0_user_id": auth0_user_id
                        }
                    )
                    
                    new_member_auth0_users = CoAuth0User(
                        userId=auth0_user["user_id"],
                        email=auth0_user.get("email"),
                        name=auth0_user.get("name"),
                        familyName=auth0_user.get("familyName"),
                        givenName=auth0_user.get("given_name"),
                        nickName=auth0_user.get("nickname"),
                        picture=auth0_user.get("picture"),
                        emailVerified=auth0_user.get("email_verified"),
                        identities=auth0_user.get("identities"),
                        createdAt=auth0_user.get("created_at"),
                        updatedAt=auth0_user.get("updated_at")
                    )

                    # Set created by fields
                    if admin_user_payload and admin_user_payload.get("user_type") == "admin":
                        admin = db.query(AdminModel).filter_by(cognitoId=admin_user_payload["user_id"]).first()
                        if admin:
                            new_member.createdByAdmin = admin.uuid
                            new_member.updatedByAdmin = admin.uuid

                    db.add(new_member)
                    db.add(new_member_auth0_users)

                    try:
                        # Commit immediately for this new member to get the UUID
                        db.commit()
                        db.refresh(new_member)

                        # Set created by member if not created by admin
                        if not admin_user_payload or admin_user_payload.get("user_type") != "admin":
                            new_member.createdByMember = new_member.uuid
                            new_member.updatedByMember = new_member.uuid
                            db.commit()
                            db.refresh(new_member)

                        # Log after state for successful creation
                        after_state = {
                            "uuid": str(new_member.uuid),
                            "firstName": new_member.firstName,
                            "lastName": new_member.lastName,
                            "loginEmail": new_member.loginEmail,
                            "membershipTier": new_member.membershipTier,
                            "communityStatus": new_member.communityStatus,
                            "auth0Id": new_member.auth0Id,
                            "dateCreated": new_member.dateCreated.isoformat() if new_member.dateCreated else None
                        }
                        
                        cloudwatch_logger.log_api_operation(
                            operation="bulk_create",
                            resource_id=str(new_member.uuid),
                            resource_type="member",
                            after_state=after_state,
                            user_id=user_id,
                            additional_context={
                                "bulk_operation": True,
                                "operation_status": "success"
                            }
                        )

                        results.append(MemberOperationResult(
                            loginEmail=str(member_data.loginEmail),
                            success=True,
                            action="created",
                            message="Member created successfully",
                            memberUuid=getattr(new_member, 'uuid')  # Use getattr to access UUID value
                        ))
                        successful_count += 1
                    except Exception as commit_error:
                        db.rollback()
                        # Clean up Auth0 user if database creation failed
                        if auth0_user_id and auth0_client:
                            try:
                                auth0_client.users.delete(auth0_user_id)
                            except:
                                pass
                        
                        results.append(MemberOperationResult(
                            loginEmail=str(member_data.loginEmail),
                            success=False,
                            action="failed",
                            message=f"Failed to commit new member: {str(commit_error)}",
                            memberUuid=None
                        ))
                        failed_count += 1

                except Exception as e:
                    db.rollback()
                    # Clean up Auth0 user if database creation failed
                    auth0_user_id = locals().get("auth0_user_id")
                    if auth0_user_id and auth0_client:
                        try:
                            auth0_client.users.delete(auth0_user_id)
                        except:
                            pass

                    results.append(MemberOperationResult(
                        loginEmail=str(member_data.loginEmail),
                        success=False,
                        action="failed",
                        message=f"Failed to create member: {str(e)}",
                        memberUuid=None
                    ))
                    failed_count += 1

        except Exception as e:
            results.append(MemberOperationResult(
                loginEmail=str(member_data.loginEmail),
                success=False,
                action="failed",
                message=f"Unexpected error: {str(e)}",
                memberUuid=None
            ))
            failed_count += 1

    # All operations are committed individually, no final commit needed
    response = BulkUpsertMembersResponse(
        totalProcessed=len(request.members),
        successful=successful_count,
        failed=failed_count,
        results=results
    )

    # Log bulk operation completion
    cloudwatch_logger.log_api_operation(
        operation="bulk_upsert_complete",
        resource_id="bulk_operation",
        resource_type="member",
        user_id=user_id,
        additional_context={
            "total_processed": len(request.members),
            "successful_count": successful_count,
            "failed_count": failed_count,
            "success_rate": f"{(successful_count / len(request.members) * 100):.1f}%" if len(request.members) > 0 else "0%"
        }
    )

    if successful_count > 0:
        return success_response(
            f"Bulk upsert completed: {successful_count} successful, {failed_count} failed",
            response.model_dump()
        )
    else:
        return success_response(
            f"Bulk upsert completed with errors: {failed_count} failed",
            response.model_dump()
        )

def get_members_with_organizations(
    db: Session, 
    page: int, 
    pageSize: int,
    firstName: Optional[str] = None,
    lastName: Optional[str] = None,
    email: Optional[str] = None,
    membershipTier: Optional[str] = None,
    communityStatus: Optional[str] = None,
    verificationStatus: Optional[str] = None,
    organizationName: Optional[str] = None,
    organizationCity: Optional[str] = None,
    organizationState: Optional[str] = None,
    organizationZip: Optional[str] = None,
    companySize: Optional[str] = None,
    dateCreatedFrom: Optional[str] = None,
    dateCreatedTo: Optional[str] = None
):
    """
    Get all members with their associated organizations, with pagination and filtering support.
    
    FILTERING LOGIC:
    1. When both member and organization filters are provided: Return only members who match member criteria AND are associated with matching organizations
    2. When only member filters are provided: Return members based solely on member criteria
    3. When only organization filters are provided: Return only members associated with matching organizations
    
    Each filter parameter is handled independently.
    Optimized to avoid N+1 query issues using proper joins.
    """
    try:
        from sqlalchemy import and_, or_
        from datetime import datetime
        
        # ===== STEP 1: Determine filter scenarios =====
        has_member_filters = any([firstName, lastName, email, membershipTier, communityStatus, verificationStatus, dateCreatedFrom, dateCreatedTo])
        has_org_filters = any([organizationName, organizationCity, organizationState, organizationZip, companySize])
        
        # ===== STEP 2: Build base query based on filter scenario =====
        if has_member_filters and has_org_filters:
            # SCENARIO 1: Both member and organization filters
            # Get members who match member criteria AND have organizations matching org criteria
            base_query = db.query(CoMember).distinct().join(
                CoRelationsMembersOrganizations,
                CoMember.id == CoRelationsMembersOrganizations.memberId
            ).join(
                CoOrganization,
                CoRelationsMembersOrganizations.organizationId == CoOrganization.id
            )
            
            # Apply member filters
            member_filter_conditions = []
            if firstName:
                member_filter_conditions.append(CoMember.firstName.ilike(f"%{firstName}%"))
            if lastName:
                member_filter_conditions.append(CoMember.lastName.ilike(f"%{lastName}%"))
            if email:
                member_filter_conditions.append(
                    or_(
                        CoMember.loginEmail.ilike(f"%{email}%"),
                        CoMember.personalBusinessEmail.ilike(f"%{email}%")
                    )
                )
            if membershipTier:
                member_filter_conditions.append(CoMember.membershipTier.ilike(f"%{membershipTier}%"))
            if communityStatus:
                member_filter_conditions.append(CoMember.communityStatus == communityStatus)
            
            # Apply verification status filter
            if verificationStatus:
                # Join with MemberVerification table for verification status filtering
                base_query = base_query.outerjoin(
                    MemberVerification,
                    CoMember.uuid == MemberVerification.member_uuid
                )
                # Handle both cases: members with verification records and those without
                if verificationStatus == "pending":
                    # For pending, include members with pending status OR members without verification records
                    member_filter_conditions.append(
                        or_(
                            MemberVerification.verification_status == verificationStatus,
                            MemberVerification.member_uuid.is_(None)
                        )
                    )
                else:
                    # For other statuses, only include members with that specific verification status
                    member_filter_conditions.append(MemberVerification.verification_status == verificationStatus)
            
            # Apply date range filters
            if dateCreatedFrom:
                try:
                    from_date = datetime.fromisoformat(dateCreatedFrom.replace('Z', '+00:00'))
                    member_filter_conditions.append(CoMember.dateCreated >= from_date)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid dateCreatedFrom format. Use ISO format: YYYY-MM-DDTHH:MM:SSZ"
                    )
            
            if dateCreatedTo:
                try:
                    to_date = datetime.fromisoformat(dateCreatedTo.replace('Z', '+00:00'))
                    member_filter_conditions.append(CoMember.dateCreated <= to_date)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid dateCreatedTo format. Use ISO format: YYYY-MM-DDTHH:MM:SSZ"
                    )
            
            # Apply organization filters
            org_filter_conditions = []
            if organizationName:
                org_filter_conditions.append(CoOrganization.name.ilike(f"%{organizationName}%"))
            if organizationCity:
                org_filter_conditions.append(CoOrganization.city.ilike(f"%{organizationCity}%"))
            if organizationState:
                org_filter_conditions.append(CoOrganization.state.ilike(f"%{organizationState}%"))
            if organizationZip:
                org_filter_conditions.append(CoOrganization.zip.ilike(f"%{organizationZip}%"))
            if companySize:
                org_filter_conditions.append(CoOrganization.companySize.ilike(f"%{companySize}%"))
            
            # Apply all filters
            if member_filter_conditions:
                base_query = base_query.filter(and_(*member_filter_conditions))
            if org_filter_conditions:
                base_query = base_query.filter(and_(*org_filter_conditions))
                
        elif has_member_filters and not has_org_filters:
            # SCENARIO 2: Only member filters
            base_query = db.query(CoMember)
            
            # Apply member filters
            member_filter_conditions = []
            if firstName:
                member_filter_conditions.append(CoMember.firstName.ilike(f"%{firstName}%"))
            if lastName:
                member_filter_conditions.append(CoMember.lastName.ilike(f"%{lastName}%"))
            if email:
                member_filter_conditions.append(
                    or_(
                        CoMember.loginEmail.ilike(f"%{email}%"),
                        CoMember.personalBusinessEmail.ilike(f"%{email}%")
                    )
                )
            if membershipTier:
                member_filter_conditions.append(CoMember.membershipTier.ilike(f"%{membershipTier}%"))
            if communityStatus:
                member_filter_conditions.append(CoMember.communityStatus == communityStatus)
            
            # Apply verification status filter
            if verificationStatus:
                # Join with MemberVerification table for verification status filtering
                base_query = base_query.outerjoin(
                    MemberVerification,
                    CoMember.uuid == MemberVerification.member_uuid
                )
                # Handle both cases: members with verification records and those without
                if verificationStatus == "pending":
                    # For pending, include members with pending status OR members without verification records
                    member_filter_conditions.append(
                        or_(
                            MemberVerification.verification_status == verificationStatus,
                            MemberVerification.member_uuid.is_(None)
                        )
                    )
                else:
                    # For other statuses, only include members with that specific verification status
                    member_filter_conditions.append(MemberVerification.verification_status == verificationStatus)
            
            # Apply date range filters
            if dateCreatedFrom:
                try:
                    from_date = datetime.fromisoformat(dateCreatedFrom.replace('Z', '+00:00'))
                    member_filter_conditions.append(CoMember.dateCreated >= from_date)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid dateCreatedFrom format. Use ISO format: YYYY-MM-DDTHH:MM:SSZ"
                    )
            
            if dateCreatedTo:
                try:
                    to_date = datetime.fromisoformat(dateCreatedTo.replace('Z', '+00:00'))
                    member_filter_conditions.append(CoMember.dateCreated <= to_date)
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid dateCreatedTo format. Use ISO format: YYYY-MM-DDTHH:MM:SSZ"
                    )
            
            if member_filter_conditions:
                base_query = base_query.filter(and_(*member_filter_conditions))
                
        elif not has_member_filters and has_org_filters:
            # SCENARIO 3: Only organization filters
            # Get members who have organizations matching org criteria
            base_query = db.query(CoMember).distinct().join(
                CoRelationsMembersOrganizations,
                CoMember.id == CoRelationsMembersOrganizations.memberId
            ).join(
                CoOrganization,
                CoRelationsMembersOrganizations.organizationId == CoOrganization.id
            )
            
            # Apply organization filters
            org_filter_conditions = []
            if organizationName:
                org_filter_conditions.append(CoOrganization.name.ilike(f"%{organizationName}%"))
            if organizationCity:
                org_filter_conditions.append(CoOrganization.city.ilike(f"%{organizationCity}%"))
            if organizationState:
                org_filter_conditions.append(CoOrganization.state.ilike(f"%{organizationState}%"))
            if organizationZip:
                org_filter_conditions.append(CoOrganization.zip.ilike(f"%{organizationZip}%"))
            if companySize:
                org_filter_conditions.append(CoOrganization.companySize.ilike(f"%{companySize}%"))
            
            if org_filter_conditions:
                base_query = base_query.filter(and_(*org_filter_conditions))
                
        else:
            # No filters provided - get all members
            base_query = db.query(CoMember)
        
        # ===== STEP 3: Get total count and paginated member IDs =====
        total_count = base_query.count()
        
        # Get paginated member IDs
        paginated_member_ids = base_query.with_entities(CoMember.id).offset((page - 1) * pageSize).limit(pageSize).all()
        member_ids = [row[0] for row in paginated_member_ids]
        
        if not member_ids:
            return success_response(
                "No members found",
                {
                    "members": [],
                    "pagination": {
                        "totalCount": total_count,
                        "currentPage": page,
                        "pageSize": pageSize,
                        "totalPages": (total_count + pageSize - 1) // pageSize,
                        "hasNext": page * pageSize < total_count,
                        "hasPrevious": page > 1
                    }
                }
            )
        
        # ===== STEP 4: Get all members for this page =====
        members = db.query(CoMember).order_by(CoMember.firstName.asc()).filter(CoMember.id.in_(member_ids)).all()
        
        # ===== STEP 5: Get verification status for all members =====
        member_uuids = [member.uuid for member in members]
        verifications = db.query(MemberVerification).filter(MemberVerification.member_uuid.in_(member_uuids)).all()
        verification_map = {verification.member_uuid: verification.verification_status for verification in verifications}
        
        # ===== STEP 6: Get all organizations for these members =====
        member_org_relations = db.query(
            CoRelationsMembersOrganizations.memberId,
            CoOrganization.uuid,
            CoOrganization.name,
            CoOrganization.phone,
            CoOrganization.email,
            CoOrganization.companySize,
            CoOrganization.city,
            CoOrganization.state,
            CoOrganization.zip,
            CoOrganization.industry,
            CoOrganization.dateCreated
        ).join(
            CoOrganization,
            CoRelationsMembersOrganizations.organizationId == CoOrganization.id
        ).filter(
            CoRelationsMembersOrganizations.memberId.in_(member_ids)
        ).all()
        
        # ===== STEP 7: Apply organization-level filters in Python (if needed) =====
        def matches_organization_filters(relation):
            """Check if an organization relation matches the organization filters"""
            if organizationName and organizationName.lower() not in (relation.name or "").lower():
                return False
            
            if organizationCity and organizationCity.lower() not in (relation.city or "").lower():
                return False
            
            if organizationState and organizationState.lower() not in (relation.state or "").lower():
                return False
            
            if organizationZip and organizationZip.lower() not in (relation.zip or "").lower():
                return False
            
            if companySize and companySize.lower() not in (relation.companySize or "").lower():
                return False
            
            return True
        
        # Filter organizations based on organization filters
        filtered_member_org_relations = []
        
        if has_org_filters:
            # Apply organization filters
            for relation in member_org_relations:
                if matches_organization_filters(relation):
                    filtered_member_org_relations.append(relation)
        else:
            # No organization filters, include all organizations
            filtered_member_org_relations = member_org_relations
        
        # ===== STEP 8: Group filtered organizations by member ID =====
        member_orgs_map = {}
        for relation in filtered_member_org_relations:
            member_id = relation.memberId
            if member_id not in member_orgs_map:
                member_orgs_map[member_id] = []
            
            org_info = {
                'name': relation.name,
                'phone': relation.phone,
                'email': relation.email,
                'companySize': relation.companySize,
                'city': relation.city,
                'state': relation.state,
                'zip': relation.zip,
                'industry': relation.industry,
                'dateCreated': relation.dateCreated
            }
            member_orgs_map[member_id].append(OrganizationInfo.model_validate(org_info))
        
        # ===== STEP 9: Build final response =====
        members_with_orgs = []
        for member in members:
            # Get verification status for this member
            member.verificationStatus = verification_map.get(member.uuid)
            
            # Get organizations for this member (empty array if no matches)
            organizations = member_orgs_map.get(member.id, [])
            
            # Create member with organizations using model_validate
            member_dict = CoMemberResponse.model_validate(member).model_dump()
            member_dict['organizations'] = organizations
            member_with_orgs = CoMemberWithOrganizations.model_validate(member_dict)
            members_with_orgs.append(member_with_orgs)

        return success_response(
            "Members with organizations retrieved successfully",
            {
                "members": [member.model_dump() for member in members_with_orgs],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": page,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": page * pageSize < total_count,
                    "hasPrevious": page > 1
                }
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving members with organizations: {str(e)}"
        )

def bulk_delete_members(request: BulkDeleteMembersRequest, db: Session, admin_user_payload: Optional[dict] = None):
    """
    Bulk delete members by UUIDs.
    Best-effort processing with detailed error reporting.
    Also deletes member-organization relations and cleans up Auth0 users.
    """
    results = []
    successful_count = 0
    failed_count = 0

    domain = settings.DOMAIN
    management_token = None
    auth0_client = None

    if domain:
        try:
            management_token = get_management_token()
            if management_token:
                auth0_client = Auth0(domain, management_token)
        except Exception as e:
            print(f"Warning: Failed to initialize Auth0 client: {str(e)}")

    for member_uuid in request.memberUuids:
        try:
            member = db.query(CoMember).filter_by(uuid=member_uuid).first()

            if not member:
                results.append(MemberDeleteResult(
                    memberUuid=member_uuid,
                    success=False,
                    message="Member not found"
                ))
                failed_count += 1
                continue

            try:
                deleted_member_uuid = getattr(member, 'uuid', None)
                deleted_login_email = getattr(member, 'loginEmail', None)
                deleted_business_email = getattr(member, 'personalBusinessEmail', None)
                deleted_auth0_id = getattr(member, 'auth0Id', None)

                db.query(CoRelationsMembersOrganizations).filter(
                    CoRelationsMembersOrganizations.memberId == member.id
                ).delete()

                if auth0_client and member.auth0Id is not None:
                    try:
                        auth0_client.users.delete(str(member.auth0Id))
                    except Exception as auth0_error:
                        print(f"Warning: Failed to delete user from Auth0 for {member_uuid}: {str(auth0_error)}")

                co_auth0_user = db.query(CoAuth0User).filter_by(userId=member.auth0Id).first()
                member_verification = db.query(MemberVerification).filter_by(member_uuid=member.uuid).first()
                
                if co_auth0_user:
                    db.delete(co_auth0_user)
                if member_verification:
                    db.delete(member_verification)
                db.delete(member)
                db.commit()

                try:
                    actor_cognito_id = None
                    if admin_user_payload:
                        # Get the Cognito ID from the JWT payload
                        actor_cognito_id = admin_user_payload.get("user_id") or admin_user_payload.get("sub")

                    context_data = {
                        "deleted_member_uuid": str(deleted_member_uuid) if deleted_member_uuid else None,
                        "deleted_email": str(deleted_login_email or deleted_business_email) if (deleted_login_email or deleted_business_email) else None,
                        "auth0_id": str(deleted_auth0_id) if deleted_auth0_id else None,
                    }

                    log_entry = CoLog(
                        userUuid=actor_cognito_id,
                        action="member_delete",
                        context=context_data,
                        purpose="Member deletion (Privacy opt-out)"
                    )
                    db.add(log_entry)
                    db.commit()
                except Exception as log_err:
                    print(f"Warning: Failed to log member deletion for {member_uuid}: {str(log_err)}")
                    db.rollback()

                results.append(MemberDeleteResult(
                    memberUuid=member_uuid,
                    success=True,
                    message="Member deleted successfully"
                ))
                successful_count += 1

            except Exception as e:
                db.rollback()
                results.append(MemberDeleteResult(
                    memberUuid=member_uuid,
                    success=False,
                    message=f"Failed to delete member: {str(e)}"
                ))
                failed_count += 1

        except Exception as e:
            results.append(MemberDeleteResult(
                memberUuid=member_uuid,
                success=False,
                message=f"Unexpected error: {str(e)}"
            ))
            failed_count += 1

    response = BulkDeleteMembersResponse(
        totalProcessed=len(request.memberUuids),
        successful=successful_count,
        failed=failed_count,
        results=results
    )

    if successful_count > 0:
        return success_response(
            f"Bulk delete completed: {successful_count} successful, {failed_count} failed",
            response.model_dump()
        )
    else:
        return success_response(
            f"Bulk delete completed with errors: {failed_count} failed",
            response.model_dump()
        )