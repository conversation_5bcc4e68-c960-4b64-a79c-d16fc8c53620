from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from controller.feature_controller import FeatureController
from db.db import get_db
from dependencies.admin_jwt import validate_jwt_token
from schemas.feature import *
from typing import List, Optional

router = APIRouter()
featureController = FeatureController()

# ========================
# Feature Flag Endpoints
# ========================

@router.post("/flags", status_code=status.HTTP_201_CREATED)
async def create_feature_flag(
    feature_flag: FeatureFlagCreate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Create a new feature flag"""
    return featureController.create_feature_flag(feature_flag, user, db)

@router.get("/flags", dependencies=[Depends(validate_jwt_token)])
async def get_all_feature_flags(
    db: Session = Depends(get_db),
    perPage: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Get all feature flags with pagination"""
    return featureController.get_all_feature_flags(db, perPage, pageSize)

@router.get("/flags/{feature_flag_uuid}", dependencies=[Depends(validate_jwt_token)])
async def get_feature_flag_by_uuid(feature_flag_uuid: str, db: Session = Depends(get_db)):
    """Get feature flag by UUID"""
    return featureController.get_feature_flag_by_uuid(feature_flag_uuid, db)

@router.put("/flags/{feature_flag_uuid}", dependencies=[Depends(validate_jwt_token)])
async def update_feature_flag(
    feature_flag_uuid: str,
    feature_flag: FeatureFlagUpdate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Update feature flag by UUID"""
    return featureController.update_feature_flag_by_uuid(feature_flag_uuid, feature_flag, user, db)

@router.delete("/flags/{feature_flag_uuid}", dependencies=[Depends(validate_jwt_token)])
async def delete_feature_flag(
    feature_flag_uuid: str,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Delete feature flag by UUID"""
    return featureController.delete_feature_flag_by_uuid(feature_flag_uuid, user, db)

@router.get("/member/{member_id}/flags", dependencies=[Depends(validate_jwt_token)])
async def get_feature_flags_by_member(member_id: int, db: Session = Depends(get_db)):
    """Get all feature flags for a specific member"""
    return featureController.get_feature_flags_by_member(member_id, db)

@router.get("/flags/search/{query}", dependencies=[Depends(validate_jwt_token)])
async def search_feature_flags(
    query: str,
    db: Session = Depends(get_db),
    perPage: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Search feature flags by feature handle or member ID"""
    return featureController.search_feature_flags(query, db, perPage, pageSize)

@router.post("/flags/bulk", status_code=status.HTTP_201_CREATED, dependencies=[Depends(validate_jwt_token)])
async def bulk_create_feature_flags(
    feature_flags: List[FeatureFlagCreate],
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Bulk create feature flags"""
    return featureController.bulk_create_feature_flags(feature_flags, user, db)

# ========================
# Bookmark Endpoints
# ========================

@router.post("/bookmarks", status_code=status.HTTP_201_CREATED)
async def create_bookmark(
    bookmark: BookmarkCreate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Create a new bookmark"""
    return featureController.create_bookmark(bookmark, user, db)

@router.get("/bookmarks", dependencies=[Depends(validate_jwt_token)])
async def get_all_bookmarks(
    db: Session = Depends(get_db),
    perPage: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Get all bookmarks with pagination"""
    return featureController.get_all_bookmarks(db, perPage, pageSize)

@router.get("/bookmarks/{bookmark_uuid}", dependencies=[Depends(validate_jwt_token)])
async def get_bookmark_by_uuid(bookmark_uuid: str, db: Session = Depends(get_db)):
    """Get bookmark by UUID"""
    return featureController.get_bookmark_by_uuid(bookmark_uuid, db)

@router.put("/bookmarks/{bookmark_uuid}", dependencies=[Depends(validate_jwt_token)])
async def update_bookmark(
    bookmark_uuid: str,
    bookmark: BookmarkUpdate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Update bookmark by UUID"""
    return featureController.update_bookmark_by_uuid(bookmark_uuid, bookmark, user, db)

@router.delete("/bookmarks/{bookmark_uuid}", dependencies=[Depends(validate_jwt_token)])
async def delete_bookmark(
    bookmark_uuid: str,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Delete bookmark by UUID"""
    return featureController.delete_bookmark_by_uuid(bookmark_uuid, user, db)

@router.get("/member/{member_id}/bookmarks", dependencies=[Depends(validate_jwt_token)])
async def get_bookmarks_by_member(member_id: int, db: Session = Depends(get_db)):
    """Get all bookmarks for a specific member"""
    return featureController.get_bookmarks_by_member(member_id, db)

@router.get("/bookmarks/search/{query}", dependencies=[Depends(validate_jwt_token)])
async def search_bookmarks(
    query: str,
    db: Session = Depends(get_db),
    perPage: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Search bookmarks by member ID or entry ID"""
    return featureController.search_bookmarks(query, db, perPage, pageSize)

@router.post("/bookmarks/bulk", status_code=status.HTTP_201_CREATED, dependencies=[Depends(validate_jwt_token)])
async def bulk_create_bookmarks(
    bookmarks: List[BookmarkCreate],
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Bulk create bookmarks"""
    return featureController.bulk_create_bookmarks(bookmarks, user, db) 