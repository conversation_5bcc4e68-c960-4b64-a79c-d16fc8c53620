
from schemas.member import CoMemberCreate, CoMemberUpdate, BulkUpsertMembersRequest, BulkDeleteMembersRequest
from schemas.member import CoMemberCreate, CoMemberUpdate
from schemas.member_export import MemberExportRequest
from services import member_api
from services.member_export_service import export_members
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception
from fastapi.responses import Response
from uuid import UUID
from typing import Optional

class MemberController:
    def get_total_member_count(self, db: Session):
        return member_api.get_total_member_count(db)

    def get_all_members(self, db: Session, page: int, pageSize: int, identityType: Optional[str] = None):
        return member_api.get_all_members(db, page=page, pageSize=pageSize, identityType=identityType)
    
    def get_member_by_auth0id(self, auth0id: str, db: Session):
        return member_api.get_member_by_auth0id(auth0id, db)
    
    def get_member_by_uuid(self, uuid: str, db: Session):
        return member_api.get_member_by_uuid(uuid, db)
    
    def update_member_by_uuid(self, uuid: str, member: CoMemberUpdate, db: Session, admin_user_payload: dict):
        return member_api.update_member_by_uuid(uuid, member, db, admin_user_payload)
    
    def get_member_by_email(self, email: str, db: Session):
        return member_api.get_member_by_email(email, db)
    
    def delete_member(self, uuid: str, db: Session, admin_user_payload: dict):
        return member_api.delete_member(uuid, db, admin_user_payload)

    # Bulk operations
    def bulk_upsert_members(self, request: BulkUpsertMembersRequest, db: Session, admin_user_payload: dict):
        return member_api.bulk_upsert_members(request, db, admin_user_payload)

    def get_members_with_organizations(
        self, 
        db: Session, 
        page: int, 
        pageSize: int,
        firstName: Optional[str] = None,
        lastName: Optional[str] = None,
        email: Optional[str] = None,
        membershipTier: Optional[str] = None,
        communityStatus: Optional[str] = None,
        verificationStatus: Optional[str] = None,
        organizationName: Optional[str] = None,
        organizationCity: Optional[str] = None,
        organizationState: Optional[str] = None,
        organizationZip: Optional[str] = None,
        companySize: Optional[str] = None,
        dateCreatedFrom: Optional[str] = None,
        dateCreatedTo: Optional[str] = None
    ):
        return member_api.get_members_with_organizations(
            db, page, pageSize, firstName, lastName, email, membershipTier, 
            communityStatus, verificationStatus, organizationName, organizationCity, organizationState, 
            organizationZip, companySize, dateCreatedFrom, dateCreatedTo
        )

    def bulk_delete_members(self, request: BulkDeleteMembersRequest, db: Session, admin_user_payload: dict):
        return member_api.bulk_delete_members(request, db, admin_user_payload)
    
    def export_members(self, request_data: MemberExportRequest, db: Session, admin_user_payload: dict):
        """
        Export members to CSV with applied filters and logging
        """
        try:
            # Extract user UUID from admin payload
            user_uuid_str = admin_user_payload.get("sub")
            if not user_uuid_str:
                raise HTTPException(status_code=401, detail="User identification not found in token")
            
            # Convert to UUID
            try:
                user_uuid = UUID(user_uuid_str)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid user UUID format")
            
            # Generate CSV content using the service
            csv_content = export_members(db, request_data, user_uuid)
            
            # Return CSV as downloadable response with proper headers
            return Response(
                content=csv_content,
                media_type="text/csv",
                headers={
                    "Content-Disposition": "attachment; filename=member_export.csv",
                    "Content-Type": "text/csv; charset=utf-8"
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")
