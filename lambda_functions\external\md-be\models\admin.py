from sqlalchemy import Column, Integer, String, DateTime, Boolean, func, text
from sqlalchemy.dialects.postgresql import ARRAY, UUID
from db.db import Base

class AdminModel(Base):
    __tablename__ = 'co_admins'

    id = Column("id", Integer, primary_key=True, index=True)
    email = Column("email", String(255), unique=True, nullable=False, index=True)
    username = Column("username", String(255), unique=True, nullable=False, index=True)

    firstName = Column("firstName", String(100), nullable=True)
    lastName = Column("lastName", String(100), nullable=True)
    phone = Column("phone", String(20), unique=True, nullable=True)
    countryCode = Column("countryCode", String(10), nullable=True)

    isActive = Column("isActive", Boolean, default=True)
    isTempPassword = Column("isTempPassword", Boolean, default=True)
    
    emailVerified = Column("emailVerified", Boolean, default=False)

    roles = Column("roles", ARRAY(String), default=[])

    cognitoId = Column("cognitoId", String(255), unique=True, nullable=False, index=True)
    uuid = Column("uuid", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), unique=True, nullable=False)
    createdBy = Column("createdBy", String(255), nullable=True)
    updatedBy = Column("updatedBy", String(255), nullable=True)

    lastLogin = Column("lastLogin", DateTime, server_default=func.now(), onupdate=func.now())
    dateCreated = Column("dateCreated", DateTime, server_default=func.now())
    dateUpdated = Column("dateUpdated", DateTime, onupdate=func.now())

    def __repr__(self):
        return f"<Admin id={self.id} email={self.email}>"


class UsedPasswordTokenModel(Base):
    __tablename__ = 'co_used_password_tokens'

    id = Column("id", Integer, primary_key=True, index=True)
    token_hash = Column("token_hash", String(255), unique=True, nullable=False, index=True)
    username = Column("username", String(255), nullable=False, index=True)
    used_at = Column("used_at", DateTime, server_default=func.now())
    expires_at = Column("expires_at", DateTime, nullable=False)

    def __repr__(self):
        return f"<UsedPasswordToken id={self.id} username={self.username}>"
