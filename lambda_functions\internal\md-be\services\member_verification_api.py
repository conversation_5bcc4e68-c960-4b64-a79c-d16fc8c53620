from fastapi import HTTPException
from sqlalchemy.orm import Session
from models.member_verification import MemberVerification
from models.member import CoMember
from models.admin import AdminModel
from schemas.member_verification import MemberVerificationUpsert, MemberVerificationResponse
from typing import List, Optional, Dict, Any
from uuid import UUID
from utils.response_utils import success_response, not_found_response


def get_total_verified_members(db: Session):
    """
    Get total count of verified members
    """
    
    # check only verified verification_status
    total_count = db.query(MemberVerification).filter(MemberVerification.verification_status == "verified").count()
    if total_count == 0:
        raise HTTPException(status_code=404, detail="No verified members found")
    return success_response(
        "Total verified members count retrieved successfully",
        {
            "totalCount": total_count
        }
    )

def get_member_verification(member_uuid: UUID, db: Session):
    """
    Get verification information for a specific member
    """
    # Check if member exists
    member = db.query(CoMember).filter(CoMember.uuid == member_uuid).first()
    if not member:
        raise HTTPException(status_code=404, detail="Member not found")
    
    # Get verification record
    verification = db.query(MemberVerification).filter(MemberVerification.member_uuid == member_uuid).first()
    if not verification:
        raise HTTPException(status_code=404, detail="Verification record not found")
    
    return success_response(
        "Member verification retrieved successfully",
        {"verification": MemberVerificationResponse.model_validate(verification)}
    )

def get_all_member_verifications(db: Session, page: int = 1, page_size: int = 10):
    """
    Get all member verifications with pagination
    """
    # Get total count
    total_count = db.query(MemberVerification).count()
    
    # Get paginated results
    verifications = (
        db.query(MemberVerification)
        .order_by(MemberVerification.dateUpdated.desc())
        .offset((page - 1) * page_size)
        .limit(page_size)
        .all()
    )
    
    return success_response(
        "Member verifications retrieved successfully",
        {
            "total_count": total_count,
            "verifications": [MemberVerificationResponse.model_validate(v) for v in verifications],
            "pagination": {
                "current_page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "has_next": page * page_size < total_count,
                "has_previous": page > 1
            }
        }
    )

def upsert_member_verification(verification_data: MemberVerificationUpsert, admin_user_payload: dict, db: Session):
    """
    Create or update member verification
    """
    # Check if member exists
    member = db.query(CoMember).filter(CoMember.uuid == verification_data.member_uuid).first()
    if not member:
        raise HTTPException(status_code=404, detail="Member not found")
    
    # Get admin information
    admin = db.query(AdminModel).filter_by(cognitoId=admin_user_payload["sub"]).first()
    if not admin:
        raise HTTPException(status_code=403, detail="Admin not found")
    
    # Check if verification record exists
    verification = db.query(MemberVerification).filter(MemberVerification.member_uuid == verification_data.member_uuid).first()
    
    if verification:
        # Update existing record
        verification.verification_status = verification_data.verification_status
        verification.verification_data = verification_data.verification_data
        verification.updatedBy = admin.uuid
        action = "updated"
    else:
        # Create new record
        verification = MemberVerification(
            member_uuid=verification_data.member_uuid,
            verification_status=verification_data.verification_status,
            verification_data=verification_data.verification_data,
            createBy=admin.uuid,
            updatedBy=admin.uuid
        )
        db.add(verification)
        action = "created"
    
    # Commit changes
    db.commit()
    db.refresh(verification)
    
    return success_response(
        f"Member verification {action} successfully",
        {"verification": MemberVerificationResponse.model_validate(verification)}
    )