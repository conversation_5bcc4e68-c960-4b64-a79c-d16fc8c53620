from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    # Auth0
    DOMAIN: str = Field(default='dev-co-uschamber.us.auth0.com')
    AUDIENCE: str = Field(default='https://dev-co-uschamber.us.auth0.com/api/v2/')
    REDIRECT_URI: str = Field(default='https://stage-console.co-nomi.app/login')
    SCOPE: str = Field(default='openid profile email')
    CLIENT_ID: str = Field(default='xsaoKLmvsTWD2h8daJG3V0XdCeDzQEJt')
    CLIENT_SECRET: str = Field(default='EbAmGKopJIcZcjOIeKjd8lrNVLNgKI8eUDL4NMvaAaxKnt5wPJM7m4HtcRRDj1Bs')
    
    # Cognito
    COGNITO_CLIENT_ID: str = Field(default='kfihrm532sale0duslibndmnn')
    COGNITO_USER_POOL_ID: str = Field(default='us-east-1_Kc99007A7')
    AWS_REGION: str = Field(default='us-east-1')

    AWS_ACCESS_KEY_ID: str = Field(..., description="AWS Access Key ID")
    AWS_SECRET_ACCESS_KEY: str = Field(..., description="AWS Secret Access Key")

    # Email Configuration
    EMAIL_FROM_ADDRESS: str = Field(default='<EMAIL>')
    EMAIL_FROM_NAME: str = Field(default='CO-Support')
    COMPANY_NAME: str = Field(default='US Chamber of Commerce')
    COMPANY_LOGO_URL: str = Field(default='https://diplomatist.com/wp-content/uploads/2024/09/us-chamber-logo-blue.25627bc.png')
    APP_BASE_URL: str = Field(default='https://stage-console.co-nomi.app')

    @property
    def JWKS_URL(self) -> str:
        # Auth0 JWKS URL
        return f"https://{self.DOMAIN}/.well-known/jwks.json" if self.DOMAIN else ''

    @property
    def ISSUER(self) -> str:
        # Auth0 Issuer
        return f"https://{self.DOMAIN}/" if self.DOMAIN else ''

    @property
    def COGNITO_JWKS_URL(self) -> str:
        # Cognito JWKS URL
        if self.AWS_REGION and self.COGNITO_USER_POOL_ID:
            return f"https://cognito-idp.{self.AWS_REGION}.amazonaws.com/{self.COGNITO_USER_POOL_ID}/.well-known/jwks.json"
        return ''

    @property
    def COGNITO_ISSUER(self) -> str:
        # Cognito Issuer
        if self.AWS_REGION and self.COGNITO_USER_POOL_ID:
            return f"https://cognito-idp.{self.AWS_REGION}.amazonaws.com/{self.COGNITO_USER_POOL_ID}"
        return ''

    @property
    def COGNITO_AUDIENCE(self) -> str:
        # Cognito Audience (App Client ID)
        return self.COGNITO_CLIENT_ID

    def validate_auth_config(self) -> dict:
        """Validate authentication configuration and return status"""
        issues = []

        # Check Auth0 configuration
        if not self.DOMAIN:
            issues.append("DOMAIN environment variable is missing (required for Auth0)")
        if not self.AUDIENCE:
            issues.append("AUDIENCE environment variable is missing (required for Auth0)")
        if not self.CLIENT_ID:
            issues.append("CLIENT_ID environment variable is missing (required for Auth0)")
        if not self.CLIENT_SECRET:
            issues.append("CLIENT_SECRET environment variable is missing (required for Auth0)")

        # Check Cognito configuration
        if not self.AWS_REGION:
            issues.append("AWS_REGION environment variable is missing (required for Cognito and AWS services)")
        if not self.COGNITO_USER_POOL_ID:
            issues.append("COGNITO_USER_POOL_ID environment variable is missing (required for Cognito)")
        if not self.COGNITO_CLIENT_ID:
            issues.append("COGNITO_CLIENT_ID environment variable is missing (required for Cognito)")

        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "auth0_configured": bool(self.DOMAIN and self.AUDIENCE and self.CLIENT_ID and self.CLIENT_SECRET),
            "cognito_configured": bool(self.AWS_REGION and self.COGNITO_USER_POOL_ID and self.COGNITO_CLIENT_ID),
        }

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
    )

settings = Settings()

# Validate configuration on startup
config_status = settings.validate_auth_config()
