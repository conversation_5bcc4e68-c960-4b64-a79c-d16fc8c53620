from pydantic import BaseModel
from typing import Optional, List, Dict
from datetime import datetime
from uuid import UUID


# -------------------------
# Role Schemas
# -------------------------

class Role(BaseModel):
    name: str
    # slug: Optional[str]
    description: Optional[str] = None


class RoleCreate(Role):
    createdBy: Optional[UUID]


class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    updatedBy: Optional[UUID] = None


class RoleResponse(Role):
    uuid: UUID
    # slug: Optional[str]
    createdBy: Optional[UUID]
    updatedBy: Optional[UUID]
    dateCreated: datetime
    dateUpdated: datetime
    description: Optional[str] = None

    class Config:
        orm_mode = True
        from_attributes = True


class CreatorInfo(BaseModel):
    """Schema for creator/updater information"""
    uuid: Optional[UUID]
    username: str


class RoleResponseEnriched(Role):
    """Role response with enriched creator information"""
    uuid: UUID
    slug: Optional[str]
    createdBy: Optional[CreatorInfo]
    updatedBy: Optional[UUID]
    dateCreated: datetime
    dateUpdated: datetime
    description: Optional[str] = None

    class Config:
        orm_mode = True
        from_attributes = True
        
# -------------------------
# Module Schemas
# -------------------------

class Module(BaseModel):
    name: str


class ModuleCreate(Module):
    createdBy: Optional[UUID]


class ModuleUpdate(Module):
    updatedBy: Optional[UUID]


class ModuleResponse(Module):
    uuid: UUID
    slug: Optional[str]
    createdBy: Optional[UUID]
    updatedBy: Optional[UUID]
    dateCreated: datetime
    dateUpdated: datetime

    class Config:
        orm_mode = True
        from_attributes = True
    
# -------------------------
# RoleModulePermission Schemas
# -------------------------
        
class RoleModulePermissionBase(BaseModel):
    roleId: int
    moduleId: int
    create: bool = False
    update: bool = False
    delete: bool = False
    view: bool = False


class RoleModulePermissionCreate(RoleModulePermissionBase):
    createdBy: Optional[UUID]


class RoleModulePermissionUpdate(BaseModel):
    create: Optional[bool]
    update: Optional[bool]
    delete: Optional[bool]
    view: Optional[bool]
    updatedBy: Optional[UUID]


class RoleModulePermissionResponse(RoleModulePermissionBase):
    createdBy: Optional[UUID]
    updatedBy: Optional[UUID]
    dateCreated: datetime
    dateUpdated: datetime

    class Config:
        orm_mode = True
        from_attributes = True
        


# -------------------------
# Advanced RBAC Schemas
# -------------------------

class RoleWithPermissions(BaseModel):
    """Schema for a role with all its permissions"""
    role: RoleResponse
    permissions: List[RoleModulePermissionResponse]

    class Config:
        orm_mode = True
        from_attributes = True


class ModuleWithAllPermissions(BaseModel):
    """Schema for a module with all role permissions"""
    module: ModuleResponse
    rolePermissions: List[RoleModulePermissionResponse]

    class Config:
        orm_mode = True
        from_attributes = True


class PermissionSummary(BaseModel):
    """Schema for permission summary by role and module"""
    roleName: str
    moduleName: str
    create: bool = False
    update: bool = False
    delete: bool = False
    view: bool = False

    class Config:
        orm_mode = True
        from_attributes = True


# -------------------------
# Role Upsert with Permissions Schemas
# -------------------------

class ModulePermissionRequest(BaseModel):
    create: bool = False
    update: bool = False
    view: bool = False
    delete: bool = False

class RoleDataRequest(BaseModel):
    name: str
    description: Optional[str] = None

class RoleUpsertWithPermissionsRequest(BaseModel):
    
    role: RoleDataRequest
    permissions: Dict[str, ModulePermissionRequest]