from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from models.member import CoMember
from models.organization import CoOrganization, CoRelationsMembersOrganizations
from models.member_verification import MemberVerification
from models.log import CoLog
from schemas.member_export import MemberExportRequest, MemberExportFilters
from typing import List, Dict, Any, Optional
import csv
import io
from datetime import datetime
from uuid import UUID

def build_member_export_query(db: Session, filters: MemberExportFilters):
    """
    Build a query that applies member filters first, then organization filters
    Uses the same approach as the API endpoint
    """
    # ===== STEP 1: Apply member-level filters only =====
    base_query = db.query(CoMember)
    member_filter_conditions = []

    if filters.firstName:
        member_filter_conditions.append(CoMember.firstName.ilike(f"%{filters.firstName}%"))
    
    if filters.lastName:
        member_filter_conditions.append(CoMember.lastName.ilike(f"%{filters.lastName}%"))
    
    if filters.email:
        member_filter_conditions.append(
            or_(
                CoMember.loginEmail.ilike(f"%{filters.email}%"),
                CoMember.personalBusinessEmail.ilike(f"%{filters.email}%")
            )
        )
    
    if filters.membershipTier:
        member_filter_conditions.append(CoMember.membershipTier.ilike(f"%{filters.membershipTier}%"))
    
    if filters.communityStatus:
        member_filter_conditions.append(CoMember.communityStatus == filters.communityStatus)
    
    if filters.verificationStatus:
        # Join with MemberVerification table for verification status filtering
        base_query = base_query.outerjoin(
            MemberVerification,
            CoMember.uuid == MemberVerification.member_uuid
        )
        # Handle both cases: members with verification records and those without
        if filters.verificationStatus == "pending":
            # For pending, include members with pending status OR members without verification records
            member_filter_conditions.append(
                or_(
                    MemberVerification.verification_status == filters.verificationStatus,
                    MemberVerification.member_uuid.is_(None)
                )
            )
        else:
            # For other statuses, only include members with that specific verification status
            member_filter_conditions.append(MemberVerification.verification_status == filters.verificationStatus)
    
    if filters.dateCreatedFrom:
        try:
            date_from = datetime.fromisoformat(filters.dateCreatedFrom.replace('Z', '+00:00'))
            member_filter_conditions.append(CoMember.dateCreated >= date_from)
        except ValueError:
            pass  # Skip invalid date format
    
    if filters.dateCreatedTo:
        try:
            date_to = datetime.fromisoformat(filters.dateCreatedTo.replace('Z', '+00:00'))
            member_filter_conditions.append(CoMember.dateCreated <= date_to)
        except ValueError:
            pass  # Skip invalid date format
    
    if filters.dateCreated:
        # Filter by date (assuming we want records created on or after this date)
        member_filter_conditions.append(CoMember.dateCreated >= filters.dateCreated)

    # Apply member filters
    if member_filter_conditions:
        base_query = base_query.filter(and_(*member_filter_conditions))

    return base_query

def generate_csv_from_members_with_organizations(
    members: List[CoMember], 
    member_orgs_map: Dict[int, List[Dict]], 
    selected_fields: List[str],
    verification_map: Dict[UUID, str]
) -> str:
    """
    Generate CSV content from members and their organizations
    Creates one row per member-organization relationship (Approach 1)
    """
    if not members:
        return ""

    # Create StringIO object for CSV content
    csv_output = io.StringIO()
    
    # Map database field names to display names for CSV headers
    field_mapping = {
        'firstName': 'First Name',
        'lastName': 'Last Name', 
        'loginEmail': 'Login Email',
        'personalBusinessEmail': 'Business Email',
        'phone': 'Phone',
        'professionalTitle': 'Professional Title',
        'membershipTier': 'Membership Tier',
        'communityStatus': 'Community Status',
        'verificationStatus': 'Verification Status',
        'dateCreated': 'Date Created',
        'organizationName': 'Organization Name',
        'city': 'City',
        'state': 'State',
        'zip': 'ZIP Code',
        'industry': 'Industry',
        'companySize': 'Company Size',
        'annualRevenue': 'Annual Revenue',
        'yearFounded': 'Year Founded'
    }

    # Create CSV headers based on selected fields
    headers = [field_mapping.get(field, field) for field in selected_fields]
    
    writer = csv.writer(csv_output)
    writer.writerow(headers)
    
    # Write data rows - one row per member-organization relationship
    for member in members:
        # Get organizations for this member
        organizations = member_orgs_map.get(member.id, [])
        
        if organizations:
            # Member has organizations - create one row per organization
            for org in organizations:
                row = []
                for field in selected_fields:
                    value = get_field_value(member, org, field, verification_map)
                    row.append(format_field_value(value))
                writer.writerow(row)
        else:
            # Member has no organizations - create one row with empty org fields
            row = []
            for field in selected_fields:
                value = get_field_value(member, None, field, verification_map)
                row.append(format_field_value(value))
            writer.writerow(row)
    
    csv_content = csv_output.getvalue()
    csv_output.close()
    
    return csv_content

def get_field_value(member: CoMember, org: Optional[Dict], field: str, verification_map: Optional[Dict[UUID, str]] = None) -> Any:
    """
    Get the value for a field from either member or organization data
    """
    # Member fields
    if field in ['firstName', 'lastName', 'loginEmail', 'personalBusinessEmail', 
                 'phone', 'professionalTitle', 'membershipTier', 'communityStatus', 'dateCreated']:
        return getattr(member, field, None)
    
    # Verification status field
    elif field == 'verificationStatus':
        if verification_map:
            return verification_map.get(member.uuid)
        else:
            return None
    
    # Organization fields
    elif field in ['organizationName', 'city', 'state', 'zip', 'industry', 
                   'companySize', 'annualRevenue', 'yearFounded']:
        if org:
            return org.get(field, None)
        else:
            return None
    
    # Unknown field
    else:
        return None

def format_field_value(value: Any) -> str:
    """
    Format a field value for CSV output
    """
    if value is None:
        return ''
    elif isinstance(value, datetime):
        return value.strftime('%Y-%m-%d %H:%M:%S')
    else:
        return str(value)

def log_export_action(
    db: Session, 
    user_uuid: UUID, 
    request_data: MemberExportRequest, 
    filters_applied: Dict[str, Any]
) -> None:
    """
    Log the export action to the universal log table
    """
    try:
        context_data = {
            "selected_fields": request_data.selectedFields,
            "filters_applied": filters_applied,
            "notes": request_data.notes
        }
        
        log_entry = CoLog(
            userUuid=user_uuid,
            action="member_export",
            context=context_data,
            purpose=request_data.notes
        )
        
        db.add(log_entry)
        db.commit()
        
    except Exception as e:
        # Log the error but don't fail the export
        print(f"Warning: Failed to log export action to co_log table: {str(e)}")
        print("This is likely because the co_log table doesn't exist yet.")
        print("Please run the create_co_log_table.sql script to create the table.")
        # Don't raise the exception - export should still succeed
        db.rollback()

def export_members(
    db: Session,
    request_data: MemberExportRequest,
    user_uuid: UUID
) -> str:
    """
    Main export function that handles the complete member export process
    Uses the same filtering approach as the updated get_members_with_organizations function
    
    FILTERING LOGIC:
    1. When both member and organization filters are provided: Return only members who match member criteria AND are associated with matching organizations
    2. When only member filters are provided: Return members based solely on member criteria
    3. When only organization filters are provided: Return only members associated with matching organizations
    """
    try:
        from sqlalchemy import and_, or_
        from datetime import datetime
        
        # ===== STEP 1: Determine filter scenarios =====
        has_member_filters = any([
            request_data.filters.search and request_data.filters.search.strip(),
            request_data.filters.firstName and request_data.filters.firstName.strip(), 
            request_data.filters.lastName and request_data.filters.lastName.strip(), 
            request_data.filters.email and request_data.filters.email.strip(), 
            request_data.filters.membershipTier and request_data.filters.membershipTier.strip(),
            request_data.filters.communityStatus and request_data.filters.communityStatus.strip(),
            request_data.filters.verificationStatus,
            request_data.filters.dateCreatedFrom and request_data.filters.dateCreatedFrom.strip(),
            request_data.filters.dateCreatedTo and request_data.filters.dateCreatedTo.strip(),
            request_data.filters.dateCreated  # legacy field
        ])
        has_org_filters = any([
            request_data.filters.organizationName and request_data.filters.organizationName.strip(), 
            (request_data.filters.organizationCity and request_data.filters.organizationCity.strip()) or (request_data.filters.city and request_data.filters.city.strip()),  # support both field names
            (request_data.filters.organizationState and request_data.filters.organizationState.strip()) or (request_data.filters.state and request_data.filters.state.strip()),  # support both field names
            (request_data.filters.organizationZip and request_data.filters.organizationZip.strip()) or (request_data.filters.zip and request_data.filters.zip.strip()),  # support both field names
            request_data.filters.industry and request_data.filters.industry.strip(), 
            request_data.filters.companySize and request_data.filters.companySize.strip()
        ])
        
        # ===== STEP 2: Build base query based on filter scenario =====
        if has_member_filters and has_org_filters:
            # SCENARIO 1: Both member and organization filters
            # Get members who match member criteria AND have organizations matching org criteria
            base_query = db.query(CoMember).distinct().join(
                CoRelationsMembersOrganizations,
                CoMember.id == CoRelationsMembersOrganizations.memberId
            ).join(
                CoOrganization,
                CoRelationsMembersOrganizations.organizationId == CoOrganization.id
            )
            
            # Apply member filters
            member_filter_conditions = []
            if request_data.filters.search and request_data.filters.search.strip():
                search_term = request_data.filters.search.strip()
                member_filter_conditions.append(
                    or_(
                        CoMember.firstName.ilike(f"%{search_term}%"),
                        CoMember.lastName.ilike(f"%{search_term}%"),
                        CoMember.loginEmail.ilike(f"%{search_term}%"),
                        CoMember.personalBusinessEmail.ilike(f"%{search_term}%"),
                        CoMember.phone.ilike(f"%{search_term}%"),
                        CoMember.professionalTitle.ilike(f"%{search_term}%")
                    )
                )
            if request_data.filters.firstName and request_data.filters.firstName.strip():
                member_filter_conditions.append(CoMember.firstName.ilike(f"%{request_data.filters.firstName.strip()}%"))
            if request_data.filters.lastName and request_data.filters.lastName.strip():
                member_filter_conditions.append(CoMember.lastName.ilike(f"%{request_data.filters.lastName.strip()}%"))
            if request_data.filters.email and request_data.filters.email.strip():
                member_filter_conditions.append(
                    or_(
                        CoMember.loginEmail.ilike(f"%{request_data.filters.email.strip()}%"),
                        CoMember.personalBusinessEmail.ilike(f"%{request_data.filters.email.strip()}%")
                    )
                )
            if request_data.filters.membershipTier:
                member_filter_conditions.append(CoMember.membershipTier.ilike(f"%{request_data.filters.membershipTier}%"))
            if request_data.filters.communityStatus:
                member_filter_conditions.append(CoMember.communityStatus == request_data.filters.communityStatus)
            if request_data.filters.verificationStatus:
                # Join with MemberVerification table for verification status filtering
                base_query = base_query.outerjoin(
                    MemberVerification,
                    CoMember.uuid == MemberVerification.member_uuid
                )
                # Handle both cases: members with verification records and those without
                if request_data.filters.verificationStatus == "pending":
                    # For pending, include members with pending status OR members without verification records
                    member_filter_conditions.append(
                        or_(
                            MemberVerification.verification_status == request_data.filters.verificationStatus,
                            MemberVerification.member_uuid.is_(None)
                        )
                    )
                else:
                    # For other statuses, only include members with that specific verification status
                    member_filter_conditions.append(MemberVerification.verification_status == request_data.filters.verificationStatus)
            if request_data.filters.dateCreatedFrom:
                try:
                    date_from = datetime.fromisoformat(request_data.filters.dateCreatedFrom.replace('Z', '+00:00'))
                    member_filter_conditions.append(CoMember.dateCreated >= date_from)
                except ValueError:
                    pass  # Skip invalid date format
            if request_data.filters.dateCreatedTo:
                try:
                    date_to = datetime.fromisoformat(request_data.filters.dateCreatedTo.replace('Z', '+00:00'))
                    member_filter_conditions.append(CoMember.dateCreated <= date_to)
                except ValueError:
                    pass  # Skip invalid date format
            if request_data.filters.dateCreated:  # legacy field
                member_filter_conditions.append(CoMember.dateCreated >= request_data.filters.dateCreated)
            
            # Apply organization filters
            org_filter_conditions = []
            if request_data.filters.organizationName:
                org_filter_conditions.append(CoOrganization.name.ilike(f"%{request_data.filters.organizationName}%"))
            if request_data.filters.organizationCity or request_data.filters.city:
                city_filter = request_data.filters.organizationCity or request_data.filters.city
                org_filter_conditions.append(CoOrganization.city.ilike(f"%{city_filter}%"))
            if request_data.filters.organizationState or request_data.filters.state:
                state_filter = request_data.filters.organizationState or request_data.filters.state
                org_filter_conditions.append(CoOrganization.state.ilike(f"%{state_filter}%"))
            if request_data.filters.organizationZip or request_data.filters.zip:
                zip_filter = request_data.filters.organizationZip or request_data.filters.zip
                org_filter_conditions.append(CoOrganization.zip.ilike(f"%{zip_filter}%"))
            if request_data.filters.industry:
                org_filter_conditions.append(CoOrganization.industry.ilike(f"%{request_data.filters.industry}%"))
            if request_data.filters.companySize:
                org_filter_conditions.append(CoOrganization.companySize.ilike(f"%{request_data.filters.companySize}%"))
            
            # Apply all filters
            if member_filter_conditions:
                base_query = base_query.filter(and_(*member_filter_conditions))
            if org_filter_conditions:
                base_query = base_query.filter(and_(*org_filter_conditions))
                
        elif has_member_filters and not has_org_filters:
            # SCENARIO 2: Only member filters
            base_query = db.query(CoMember)
            
            # Apply member filters
            member_filter_conditions = []
            if request_data.filters.search and request_data.filters.search.strip():
                search_term = request_data.filters.search.strip()
                member_filter_conditions.append(
                    or_(
                        CoMember.firstName.ilike(f"%{search_term}%"),
                        CoMember.lastName.ilike(f"%{search_term}%"),
                        CoMember.loginEmail.ilike(f"%{search_term}%"),
                        CoMember.personalBusinessEmail.ilike(f"%{search_term}%"),
                        CoMember.phone.ilike(f"%{search_term}%"),
                        CoMember.professionalTitle.ilike(f"%{search_term}%")
                    )
                )
            if request_data.filters.firstName and request_data.filters.firstName.strip():
                member_filter_conditions.append(CoMember.firstName.ilike(f"%{request_data.filters.firstName.strip()}%"))
            if request_data.filters.lastName and request_data.filters.lastName.strip():
                member_filter_conditions.append(CoMember.lastName.ilike(f"%{request_data.filters.lastName.strip()}%"))
            if request_data.filters.email and request_data.filters.email.strip():
                member_filter_conditions.append(
                    or_(
                        CoMember.loginEmail.ilike(f"%{request_data.filters.email.strip()}%"),
                        CoMember.personalBusinessEmail.ilike(f"%{request_data.filters.email.strip()}%")
                    )
                )
            if request_data.filters.membershipTier:
                member_filter_conditions.append(CoMember.membershipTier.ilike(f"%{request_data.filters.membershipTier}%"))
            if request_data.filters.communityStatus:
                member_filter_conditions.append(CoMember.communityStatus == request_data.filters.communityStatus)
            if request_data.filters.verificationStatus:
                # Join with MemberVerification table for verification status filtering
                base_query = base_query.outerjoin(
                    MemberVerification,
                    CoMember.uuid == MemberVerification.member_uuid
                )
                # Handle both cases: members with verification records and those without
                if request_data.filters.verificationStatus == "pending":
                    # For pending, include members with pending status OR members without verification records
                    member_filter_conditions.append(
                        or_(
                            MemberVerification.verification_status == request_data.filters.verificationStatus,
                            MemberVerification.member_uuid.is_(None)
                        )
                    )
                else:
                    # For other statuses, only include members with that specific verification status
                    member_filter_conditions.append(MemberVerification.verification_status == request_data.filters.verificationStatus)
            if request_data.filters.dateCreatedFrom:
                try:
                    date_from = datetime.fromisoformat(request_data.filters.dateCreatedFrom.replace('Z', '+00:00'))
                    member_filter_conditions.append(CoMember.dateCreated >= date_from)
                except ValueError:
                    pass  # Skip invalid date format
            if request_data.filters.dateCreatedTo:
                try:
                    date_to = datetime.fromisoformat(request_data.filters.dateCreatedTo.replace('Z', '+00:00'))
                    member_filter_conditions.append(CoMember.dateCreated <= date_to)
                except ValueError:
                    pass  # Skip invalid date format
            if request_data.filters.dateCreated:  # legacy field
                member_filter_conditions.append(CoMember.dateCreated >= request_data.filters.dateCreated)
            
            if member_filter_conditions:
                base_query = base_query.filter(and_(*member_filter_conditions))
                
        elif not has_member_filters and has_org_filters:
            # SCENARIO 3: Only organization filters
            # Get members who have organizations matching org criteria
            base_query = db.query(CoMember).distinct().join(
                CoRelationsMembersOrganizations,
                CoMember.id == CoRelationsMembersOrganizations.memberId
            ).join(
                CoOrganization,
                CoRelationsMembersOrganizations.organizationId == CoOrganization.id
            )
            
            # Apply organization filters
            org_filter_conditions = []
            if request_data.filters.organizationName:
                org_filter_conditions.append(CoOrganization.name.ilike(f"%{request_data.filters.organizationName}%"))
            if request_data.filters.organizationCity or request_data.filters.city:
                city_filter = request_data.filters.organizationCity or request_data.filters.city
                org_filter_conditions.append(CoOrganization.city.ilike(f"%{city_filter}%"))
            if request_data.filters.organizationState or request_data.filters.state:
                state_filter = request_data.filters.organizationState or request_data.filters.state
                org_filter_conditions.append(CoOrganization.state.ilike(f"%{state_filter}%"))
            if request_data.filters.organizationZip or request_data.filters.zip:
                zip_filter = request_data.filters.organizationZip or request_data.filters.zip
                org_filter_conditions.append(CoOrganization.zip.ilike(f"%{zip_filter}%"))
            if request_data.filters.industry:
                org_filter_conditions.append(CoOrganization.industry.ilike(f"%{request_data.filters.industry}%"))
            if request_data.filters.companySize:
                org_filter_conditions.append(CoOrganization.companySize.ilike(f"%{request_data.filters.companySize}%"))
            
            if org_filter_conditions:
                base_query = base_query.filter(and_(*org_filter_conditions))
                
        else:
            # No filters provided - get all members
            base_query = db.query(CoMember)
        
        # ===== STEP 3: Get all members =====
        members = base_query.all()
        
        if not members:
            # No members found - return empty CSV
            csv_output = io.StringIO()
            writer = csv.writer(csv_output)
            
            # Create headers
            field_mapping = {
                'firstName': 'First Name', 'lastName': 'Last Name', 
                'loginEmail': 'Login Email', 'personalBusinessEmail': 'Business Email',
                'phone': 'Phone', 'professionalTitle': 'Professional Title',
                'membershipTier': 'Membership Tier', 'communityStatus': 'Community Status',
                'verificationStatus': 'Verification Status', 'dateCreated': 'Date Created', 
                'organizationName': 'Organization Name', 'city': 'City', 'state': 'State', 
                'zip': 'ZIP Code', 'industry': 'Industry', 'companySize': 'Company Size',
                'annualRevenue': 'Annual Revenue', 'yearFounded': 'Year Founded'
            }
            headers = [field_mapping.get(field, field) for field in request_data.selectedFields]
            writer.writerow(headers)
            
            csv_content = csv_output.getvalue()
            csv_output.close()
            
            # Log the export action
            log_export_action(db, user_uuid, request_data, {})
            return csv_content
        
        # ===== STEP 4: Get verification status for all members =====
        member_uuids = [member.uuid for member in members]
        verifications = db.query(MemberVerification).filter(MemberVerification.member_uuid.in_(member_uuids)).all()
        verification_map = {verification.member_uuid: verification.verification_status for verification in verifications}
        
        # ===== STEP 5: Get all organizations for these members =====
        member_ids = [member.id for member in members]
        
        member_org_relations = db.query(
            CoRelationsMembersOrganizations.memberId,
            CoOrganization.uuid,
            CoOrganization.name,
            CoOrganization.phone,
            CoOrganization.email,
            CoOrganization.companySize,
            CoOrganization.city,
            CoOrganization.state,
            CoOrganization.zip,
            CoOrganization.industry,
            CoOrganization.annualRevenue,
            CoOrganization.yearFounded,
            CoOrganization.dateCreated
        ).join(
            CoOrganization,
            CoRelationsMembersOrganizations.organizationId == CoOrganization.id
        ).filter(
            CoRelationsMembersOrganizations.memberId.in_(member_ids)
        ).all()
        
        # ===== STEP 6: Apply organization-level filters in Python (if needed) =====
        def matches_organization_filters(relation):
            """Check if an organization relation matches the organization filters"""
            if request_data.filters.organizationName and request_data.filters.organizationName.lower() not in (relation.name or "").lower():
                return False
            
            city_filter = request_data.filters.organizationCity or request_data.filters.city
            if city_filter and city_filter.lower() not in (relation.city or "").lower():
                return False
            
            state_filter = request_data.filters.organizationState or request_data.filters.state
            if state_filter and state_filter.lower() not in (relation.state or "").lower():
                return False
            
            zip_filter = request_data.filters.organizationZip or request_data.filters.zip
            if zip_filter and zip_filter.lower() not in (relation.zip or "").lower():
                return False
            
            if request_data.filters.industry and request_data.filters.industry.lower() not in (relation.industry or "").lower():
                return False
            
            if request_data.filters.companySize and request_data.filters.companySize.lower() not in (relation.companySize or "").lower():
                return False
            
            return True
        
        # Filter organizations based on organization filters
        filtered_member_org_relations = []
        
        if has_org_filters:
            # Apply organization filters
            for relation in member_org_relations:
                if matches_organization_filters(relation):
                    filtered_member_org_relations.append(relation)
        else:
            # No organization filters, include all organizations
            filtered_member_org_relations = member_org_relations
        
        # ===== STEP 7: Group filtered organizations by member ID =====
        member_orgs_map = {}
        for relation in filtered_member_org_relations:
            member_id = relation.memberId
            if member_id not in member_orgs_map:
                member_orgs_map[member_id] = []
            
            org_info = {
                'name': relation.name,
                'phone': relation.phone,
                'email': relation.email,
                'companySize': relation.companySize,
                'city': relation.city,
                'state': relation.state,
                'zip': relation.zip,
                'industry': relation.industry,
                'annualRevenue': relation.annualRevenue,
                'yearFounded': relation.yearFounded,
                'dateCreated': relation.dateCreated
            }
            member_orgs_map[member_id].append(org_info)
        
        # ===== STEP 8: Generate CSV content =====
        csv_content = generate_csv_from_members_with_organizations(
            members, member_orgs_map, request_data.selectedFields, verification_map
        )
        
        # ===== STEP 9: Log the export action =====
        # Prepare filters for logging (only include non-None values)
        filters_applied = {}
        if request_data.filters:
            for field, value in request_data.filters.model_dump().items():
                if value is not None:
                    if isinstance(value, datetime):
                        filters_applied[field] = value.isoformat()
                    else:
                        filters_applied[field] = value
        
        log_export_action(db, user_uuid, request_data, filters_applied)
        
        return csv_content

    except Exception as e:
        raise Exception(f"Error exporting members: {str(e)}") 