from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from models.organization import CoOrganization, CoOrganizationVerifiedData, CoRelationsMembersOrganizations, CoMembersAwards
from models.member import CoMember
from schemas.organization import *
from schemas.member import CoMemberResponse
from typing import List, Optional
from datetime import datetime
from utils.response_utils import (
    success_response, created_response, not_found_response,
    conflict_response, bad_request_response, internal_server_error_response
)

# ========================
# Organization CRUD Operations
# ========================

def create_organization(organization: OrganizationCreate, user: dict, db: Session):
    """Create a new organization"""
    try:
        user_id = user.get("sub") or user.get("id")
        
        new_organization = CoOrganization(
            name=organization.name,
            address1=organization.address1,
            address2=organization.address2,
            city=organization.city,
            state=organization.state,
            zip=organization.zip,
            phone=organization.phone,
            annualRevenue=organization.annualRevenue,
            industry=organization.industry,
            yearFounded=organization.yearFounded,
            businessProfileElementId=organization.businessProfileElementId,
            email=organization.email,
            companySize=organization.companySize,
            createdBy=user_id,
            updatedBy=user_id
        )
        
        db.add(new_organization)
        db.commit()
        db.refresh(new_organization)
        
        # Use Pydantic to directly validate the SQLAlchemy object
        organization_response = OrganizationResponse.model_validate(new_organization)

        return created_response(
            "Organization created successfully",
            {"organization": organization_response}
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error creating organization: {str(e)}"}
        )
        
def create_organization_by_member(organization: OrganizationCreate, user: dict, db: Session):
    """
    Upsert organization by member - create if doesn't exist, update if exists
    """
    try:
        auth0_id = user.get("sub") or user.get("user_id")
        if not auth0_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": "Auth0 user ID is required to create organization"}
            )

        member = db.query(CoMember).filter(CoMember.auth0Id == auth0_id).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Member with Auth0 ID {auth0_id} not found"}
            )

        # Check if member already has an organization
        existing_relation = db.query(CoRelationsMembersOrganizations).filter(
            CoRelationsMembersOrganizations.memberId == member.id
        ).first()

        if existing_relation:
            # Update existing organization
            existing_organization = db.query(CoOrganization).filter(
                CoOrganization.id == existing_relation.organizationId
            ).first()

            if existing_organization:
                # Update organization fields
                existing_organization.name = organization.name
                existing_organization.address1 = organization.address1
                existing_organization.address2 = organization.address2
                existing_organization.city = organization.city
                existing_organization.state = organization.state
                existing_organization.zip = organization.zip
                existing_organization.phone = organization.phone
                existing_organization.annualRevenue = organization.annualRevenue
                existing_organization.industry = organization.industry
                existing_organization.yearFounded = organization.yearFounded
                existing_organization.businessProfileElementId = organization.businessProfileElementId
                existing_organization.email = organization.email
                existing_organization.companySize = organization.companySize
                existing_organization.updatedBy = member.uuid

                db.commit()
                db.refresh(existing_organization)

                return success_response(
                    "Organization updated successfully",
                    {"organization": OrganizationResponse.model_validate(existing_organization)}
                )

        # Create new organization if none exists
        new_organization = CoOrganization(
            name=organization.name,
            address1=organization.address1,
            address2=organization.address2,
            city=organization.city,
            state=organization.state,
            zip=organization.zip,
            phone=organization.phone,
            annualRevenue=organization.annualRevenue,
            industry=organization.industry,
            yearFounded=organization.yearFounded,
            businessProfileElementId=organization.businessProfileElementId,
            email=organization.email,
            companySize=organization.companySize,
            createdBy=member.uuid,
            updatedBy=member.uuid
        )

        db.add(new_organization)
        db.commit()
        db.refresh(new_organization)

        # Create the membership relation
        new_membership = CoRelationsMembersOrganizations(
            memberId=member.id,
            organizationId=new_organization.id,
            createdBy=member.uuid
        )
        db.add(new_membership)
        db.commit()

        return created_response(
            "Organization created successfully",
            {"organization": OrganizationResponse.model_validate(new_organization)}
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error creating/updating organization: {str(e)}"}
        )

def get_all_organizations(db: Session, page: int, pageSize: int,
                         name: Optional[str] = None, city: Optional[str] = None,
                         state: Optional[str] = None, zip: Optional[str] = None,
                         companySize: Optional[str] = None, industry: Optional[str] = None,
                         yearFounded: Optional[str] = None, phone: Optional[str] = None,
                         email: Optional[str] = None, dateCreatedFrom: Optional[str] = None,
                         dateCreatedTo: Optional[str] = None, memberCount: Optional[int] = None,
                         sortBy: str = "dateUpdated", sortOrder: str = "desc",
                         annualRevenueMin: Optional[str] = None, annualRevenueMax: Optional[str] = None,
                         yearFoundedMin: Optional[str] = None, yearFoundedMax: Optional[str] = None):
    """Get all organizations with comprehensive filtering and pagination"""
    try:
        # Start with base query
        query = db.query(CoOrganization)
        
        # Apply filters
        filters = []
        
        # Basic text filters (case-insensitive partial matching)
        if name:
            filters.append(CoOrganization.name.ilike(f"%{name}%"))
        if city:
            filters.append(CoOrganization.city.ilike(f"%{city}%"))
        if state:
            filters.append(CoOrganization.state.ilike(f"%{state}%"))
        if zip:
            filters.append(CoOrganization.zip.ilike(f"%{zip}%"))
        if companySize:
            filters.append(CoOrganization.companySize.ilike(f"%{companySize}%"))
        if industry:
            filters.append(CoOrganization.industry.ilike(f"%{industry}%"))
        if yearFounded:
            filters.append(CoOrganization.yearFounded.ilike(f"%{yearFounded}%"))
        if phone:
            filters.append(CoOrganization.phone.ilike(f"%{phone}%"))
        if email:
            filters.append(CoOrganization.email.ilike(f"%{email}%"))
        
        # Date range filters
        if dateCreatedFrom:
            try:
                from_date = datetime.strptime(dateCreatedFrom, "%Y-%m-%d")
                filters.append(CoOrganization.dateCreated >= from_date)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"message": "Invalid dateCreatedFrom format. Use YYYY-MM-DD"}
                )
        
        if dateCreatedTo:
            try:
                to_date = datetime.strptime(dateCreatedTo, "%Y-%m-%d")
                # Add one day to include the entire day
                to_date = to_date.replace(hour=23, minute=59, second=59)
                filters.append(CoOrganization.dateCreated <= to_date)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"message": "Invalid dateCreatedTo format. Use YYYY-MM-DD"}
                )
        
        # Revenue range filters
        if annualRevenueMin:
            filters.append(CoOrganization.annualRevenue >= annualRevenueMin)
        if annualRevenueMax:
            filters.append(CoOrganization.annualRevenue <= annualRevenueMax)
        
        # Year founded range filters
        if yearFoundedMin:
            filters.append(CoOrganization.yearFounded >= yearFoundedMin)
        if yearFoundedMax:
            filters.append(CoOrganization.yearFounded <= yearFoundedMax)
        
        # Member count filter (requires subquery to count relations)
        if memberCount is not None:
            member_count_subquery = db.query(
                CoRelationsMembersOrganizations.organizationId,
                func.count(CoRelationsMembersOrganizations.memberId).label('member_count')
            ).group_by(CoRelationsMembersOrganizations.organizationId).subquery()
            
            query = query.join(member_count_subquery, 
                             CoOrganization.id == member_count_subquery.c.organizationId)
            filters.append(member_count_subquery.c.member_count == memberCount)
        
        # Apply all filters
        if filters:
            query = query.filter(and_(*filters))
        
        # Apply sorting
        sort_column = getattr(CoOrganization, sortBy, CoOrganization.dateUpdated)
        if sortOrder.lower() == "asc":
            query = query.order_by(asc(sort_column))
        else:
            query = query.order_by(desc(sort_column))
        
        # Get total count for pagination
        total_count = query.count()
        
        # Apply pagination
        organizations = query.offset((page - 1) * pageSize).limit(pageSize).all()
        
        if not organizations:
            return success_response("No organizations found", {
                "organizations": [],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": page,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": page * pageSize < total_count,
                    "hasPrevious": page > 1
                }
            })
        
        # Use Pydantic to directly validate SQLAlchemy objects
        organizations_list = [OrganizationResponse.model_validate(org) for org in organizations]
        
        return success_response(
            "Organizations retrieved successfully",
            {
                "organizations": organizations_list,
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": page,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": page * pageSize < total_count,
                    "hasPrevious": page > 1
                }
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving organizations: {str(e)}"}
        )

def get_organization_by_uuid(uuid: str, db: Session):
    """Get organization by UUID"""
    try:
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == uuid).first()
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization with UUID {uuid} not found"}
            )
        
        # Use Pydantic to directly validate the SQLAlchemy object
        organization_response = OrganizationResponse.model_validate(organization)

        return success_response(
            "Organization retrieved successfully",
            {"organization": organization_response}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving organization: {str(e)}"}
        )

def update_organization(uuid: str, organization: OrganizationUpdate, user: dict, db: Session):
    """Update organization by UUID"""
    try:
        existing_organization = db.query(CoOrganization).filter(CoOrganization.uuid == uuid).first()
        
        if not existing_organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization with UUID {uuid} not found"}
            )
        
        user_id = user.get("sub") or user.get("id")
        
        # Update fields
        for key, value in organization.model_dump(exclude_unset=True).items():
            if hasattr(existing_organization, key):
                setattr(existing_organization, key, value)
        
        if user_id is not None:
            existing_organization.updatedBy = user_id
        db.commit()
        db.refresh(existing_organization)

        # Use Pydantic to directly validate the SQLAlchemy object
        organization_response = OrganizationResponse.model_validate(existing_organization)

        return success_response(
            "Organization updated successfully",
            {"organization": organization_response}
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error updating organization: {str(e)}"}
        )

def delete_organization(uuid: str, user: dict, db: Session):
    """Delete organization by UUID"""
    try:
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == uuid).first()
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization with UUID {uuid} not found"}
            )
        
        db.delete(organization)
        db.commit()
        
        return success_response(f"Organization '{organization.name}' deleted successfully")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error deleting organization: {str(e)}"}
        )

def get_organization_with_verified_data(uuid: str, db: Session):
    """Get organization with verified data"""
    try:
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == uuid).first()
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization with UUID {uuid} not found"}
            )
        
        # Use Pydantic to directly validate the SQLAlchemy object
        organization_response = OrganizationResponse.model_validate(organization)

        # Get verified data if exists
        verified_data = db.query(CoOrganizationVerifiedData).filter(
            CoOrganizationVerifiedData.organizationId == organization.id
        ).first()

        # Create the response with verified data
        response_data = organization_response.model_dump()

        if verified_data:
            # Add organizationUuid to verified_data for validation
            verified_data.organizationUuid = organization.uuid
            verified_data_response = OrganizationVerifiedDataResponse.model_validate(verified_data)
            response_data["verifiedData"] = verified_data_response.model_dump()
        else:
            response_data["verifiedData"] = None

        organization_with_verified_data = OrganizationWithVerifiedData.model_validate(response_data)

        return success_response(
            "Organization with verified data retrieved successfully",
            {"organization": organization_with_verified_data}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving organization: {str(e)}"}
        )

def get_organization_with_relations(uuid: str, db: Session):
    """Get organization with member relations and awards"""
    try:
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == uuid).first()
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization with UUID {uuid} not found"}
            )
        
        # Use Pydantic to directly validate the SQLAlchemy object
        organization_response = OrganizationResponse.model_validate(organization)
        
        # Get member relations
        relations = db.query(CoRelationsMembersOrganizations).filter(
            CoRelationsMembersOrganizations.organizationId == organization.id
        ).all()

        relations_list = []
        for rel in relations:
            # Get member UUID for the relation
            member = db.query(CoMember).filter(CoMember.id == rel.memberId).first()
            if member:
                # Create a temporary object with the required fields
                relation_data = type('obj', (object,), {
                    'memberUuid': member.uuid,
                    'organizationUuid': organization.uuid,
                    'createdBy': rel.createdBy,
                    'dateCreated': rel.dateCreated
                })()
                relations_list.append(MemberOrganizationRelationResponse.model_validate(relation_data))
        
        # Get awards
        awards = db.query(CoMembersAwards).filter(
            CoMembersAwards.organizationId == organization.id
        ).all()

        awards_list = []
        for award in awards:
            # Get member UUID for the award
            member = db.query(CoMember).filter(CoMember.id == award.memberId).first()
            if member:
                # Add UUIDs to the award object for validation
                award.memberUuid = member.uuid
                award.organizationUuid = organization.uuid
                awards_list.append(MemberAwardResponse.model_validate(award))

        # Create the response with relations and awards
        response_data = organization_response.model_dump()
        response_data["memberRelations"] = [rel.model_dump() for rel in relations_list]
        response_data["awards"] = [award.model_dump() for award in awards_list]

        organization_with_relations = OrganizationWithRelations.model_validate(response_data)

        return success_response(
            "Organization with relations retrieved successfully",
            {"organization": organization_with_relations}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving organization with relations: {str(e)}"}
        )

# ========================
# Organization Verified Data Operations
# ========================

def create_organization_verified_data(verified_data: OrganizationVerifiedDataCreate, user: dict, db: Session):
    """Create verified data for an organization"""
    try:
        user_id = user.get("sub") or user.get("id")
        
        # Check if organization exists by UUID
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == verified_data.organizationUuid).first()

        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization {verified_data.organizationUuid} not found"}
            )
        
        # Check if verified data already exists
        existing_verified_data = db.query(CoOrganizationVerifiedData).filter(
            CoOrganizationVerifiedData.organizationId == organization.id
        ).first()
        
        if existing_verified_data:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={"message": f"Verified data already exists for organization {organization.name}"}
            )
        
        new_verified_data = CoOrganizationVerifiedData(
            organizationId=organization.id,
            name=verified_data.name,
            address=verified_data.address,
            city=verified_data.city,
            state=verified_data.state,
            zip=verified_data.zip,
            phone=verified_data.phone,
            ein=verified_data.ein,
            industry=verified_data.industry,
            foundingYear=verified_data.foundingYear,
            verificationStatus=verified_data.verificationStatus,
            verificationType=verified_data.verificationType,
            createdBy=user_id,
            updatedBy=user_id
        )
        
        db.add(new_verified_data)
        db.commit()
        db.refresh(new_verified_data)
        
        # Add organizationUuid to verified_data for validation
        new_verified_data.organizationUuid = organization.uuid
        verified_data_response = OrganizationVerifiedDataResponse.model_validate(new_verified_data)

        return created_response(
            "Organization verified data created successfully",
            {"verifiedData": verified_data_response}
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error creating verified data: {str(e)}"}
        )

def get_organization_verified_data_by_uuid(uuid: str, db: Session):
    """Get verified data by UUID"""
    try:
        verified_data = db.query(CoOrganizationVerifiedData).filter(CoOrganizationVerifiedData.uuid == uuid).first()
        
        if not verified_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Verified data with UUID {uuid} not found"}
            )
        
        # Get the organization to include its UUID
        organization = db.query(CoOrganization).filter(CoOrganization.id == verified_data.organizationId).first()

        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": "Associated organization not found"}
            )

        # Add organizationUuid to verified_data for validation
        verified_data.organizationUuid = organization.uuid
        verified_data_response = OrganizationVerifiedDataResponse.model_validate(verified_data)

        return success_response(
            "Verified data retrieved successfully",
            {"verifiedData": verified_data_response}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving verified data: {str(e)}"}
        )

def update_organization_verified_data_by_uuid(uuid: str, verified_data: OrganizationVerifiedDataUpdate, user: dict, db: Session):
    """Update verified data by UUID"""
    try:
        existing_data = db.query(CoOrganizationVerifiedData).filter(CoOrganizationVerifiedData.uuid == uuid).first()
        
        if not existing_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Verified data with UUID {uuid} not found"}
            )
        
        user_id = user.get("sub") or user.get("id")
        
        # Update fields
        for key, value in verified_data.model_dump(exclude_unset=True).items():
            if hasattr(existing_data, key):
                setattr(existing_data, key, value)
        
        if user_id is not None:
            existing_data.updatedBy = user_id
        db.commit()
        db.refresh(existing_data)

        # Get organization UUID for response
        organization = db.query(CoOrganization).filter(CoOrganization.id == existing_data.organizationId).first()
        if organization:
            existing_data.organizationUuid = organization.uuid
            verified_data_response = OrganizationVerifiedDataResponse.model_validate(existing_data)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": "Associated organization not found"}
            )

        return success_response(
            "Verified data updated successfully",
            {"verifiedData": verified_data_response}
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error updating verified data: {str(e)}"}
        )

def delete_organization_verified_data_by_uuid(uuid: str, db: Session):
    """Delete verified data by UUID"""
    try:
        verified_data = db.query(CoOrganizationVerifiedData).filter(CoOrganizationVerifiedData.uuid == uuid).first()
        
        if not verified_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Verified data with UUID {uuid} not found"}
            )
        
        db.delete(verified_data)
        db.commit()
        
        return success_response("Verified data deleted successfully")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error deleting verified data: {str(e)}"}
        )

# ========================
# Member Organization Relations Operations
# ========================

def create_member_organization_relation(relation: MemberOrganizationRelationCreate, user: dict, db: Session):
    """Create a member-organization relation"""
    try:
        user_id = user.get("sub") or user.get("id")

        # Find member by UUID
        member = db.query(CoMember).filter(CoMember.uuid == relation.memberUuid).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Member with UUID {relation.memberUuid} not found"}
            )

        # Find organization by UUID
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == relation.organizationUuid).first()
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization with UUID {relation.organizationUuid} not found"}
            )

        # Check if relation already exists
        existing_relation = db.query(CoRelationsMembersOrganizations).filter(
            CoRelationsMembersOrganizations.memberId == member.id,
            CoRelationsMembersOrganizations.organizationId == organization.id
        ).first()

        if existing_relation:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={"message": f"Relation already exists between member {relation.memberUuid} and organization {relation.organizationUuid}"}
            )

        new_relation = CoRelationsMembersOrganizations(
            memberId=member.id,
            organizationId=organization.id,
            createdBy=user_id
        )

        db.add(new_relation)
        db.commit()
        db.refresh(new_relation)

        # Create response with UUIDs
        relation_data = type('obj', (object,), {
            'memberUuid': relation.memberUuid,
            'organizationUuid': relation.organizationUuid,
            'createdBy': new_relation.createdBy,
            'dateCreated': new_relation.dateCreated
        })()

        return created_response(
            "Member-organization relation created successfully",
            {"relation": MemberOrganizationRelationResponse.model_validate(relation_data)}
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error creating relation: {str(e)}"}
        )

def get_member_organization_relations(db: Session, memberid: Optional[str] = None, organizationid: Optional[str] = None):
    """Get member-organization relations"""
    try:
        query = db.query(CoRelationsMembersOrganizations)

        # Filter by member UUID if provided
        if memberid:
            member = db.query(CoMember).filter(CoMember.uuid == memberid).first()
            if member:
                query = query.filter(CoRelationsMembersOrganizations.memberId == member.id)
            else:
                return success_response("No relations found", {"relations": []})

        # Filter by organization UUID if provided
        if organizationid:
            organization = db.query(CoOrganization).filter(CoOrganization.uuid == organizationid).first()
            if organization:
                query = query.filter(CoRelationsMembersOrganizations.organizationId == organization.id)
            else:
                return success_response("No relations found", {"relations": []})

        relations = query.all()

        if not relations:
            return success_response("No relations found", {"relations": []})

        relations_list = []
        for rel in relations:
            # Get member and organization UUIDs
            member = db.query(CoMember).filter(CoMember.id == rel.memberId).first()
            organization = db.query(CoOrganization).filter(CoOrganization.id == rel.organizationId).first()

            if member and organization:
                # Create a temporary object with the required fields
                relation_data = type('obj', (object,), {
                    'memberUuid': member.uuid,
                    'organizationUuid': organization.uuid,
                    'createdBy': rel.createdBy,
                    'dateCreated': rel.dateCreated
                })()
                relations_list.append(MemberOrganizationRelationResponse.model_validate(relation_data))

        return success_response(
            "Relations retrieved successfully",
            {"relations": relations_list}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving relations: {str(e)}"}
        )

def delete_member_organization_relation_by_uuid(relation: MemberOrganizationRelationCreate, db: Session):
    """Delete member-organization relation by UUID"""
    try:
        member = db.query(CoMember).filter(CoMember.uuid == relation.memberUuid).first()
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == relation.organizationUuid).first()

        if not member or not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Relation with UUID {relation.memberUuid} and {relation.organizationUuid} not found"}
            )
        
        # Find and delete the relation
        db_relation = db.query(CoRelationsMembersOrganizations).filter(
            CoRelationsMembersOrganizations.memberId == member.id,
            CoRelationsMembersOrganizations.organizationId == organization.id
        ).first()
        
        if not db_relation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Relation with UUID {relation.memberUuid} and {relation.organizationUuid} not found"}
            )
        
        db.delete(db_relation)
        db.commit()
        
        return success_response("Member-organization relation deleted successfully")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error deleting relation: {str(e)}"}
        )
        
# ========================
# Member Awards Operations
# ========================

def create_member_award(award: MemberAwardCreate, user: dict, db: Session):
    """Create a new member award"""
    try:
        user_id = user.get("sub") or user.get("id")
        
        # Verify member exists
        member = db.query(CoMember).filter(CoMember.uuid == award.memberUuid).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Member {award.memberUuid} not found"}
            )

        # Verify organization exists
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == award.organizationUuid).first()
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization {award.organizationUuid} not found"}
            )
        
        new_award = CoMembersAwards(
            memberId=member.id,
            organizationId=organization.id,
            awardListingElementId=award.awardListingElementId,
            openWaterUserId=award.openWaterUserId,
            openWaterApplicationId=award.openWaterApplicationId,
            status=award.status,
            progress=award.progress,
            categories=award.categories,
            isDisqualified=award.isDisqualified,
            isPreviousWinner=award.isPreviousWinner,
            isQualified=award.isQualified,
            isJudged=award.isJudged,
            isPaid=award.isPaid,
            isWinner=award.isWinner,
            winnerTypes=award.winnerTypes,
            applicationLink=award.applicationLink,
            startedDate=award.startedDate,
            submittedDate=award.submittedDate,
            createdBy=user_id,
            updatedBy=user_id
        )
        
        db.add(new_award)
        db.commit()
        db.refresh(new_award)
        
        # Add UUIDs to the award object for validation
        new_award.memberUuid = member.uuid
        new_award.organizationUuid = organization.uuid

        award_response = MemberAwardResponse.model_validate(new_award)
        
        return created_response(
            "Member award created successfully",
            {"award": award_response}
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error creating member award: {str(e)}"}
        )

def get_member_awards(db: Session, memberid: Optional[str] = None, organizationid: Optional[str] = None, perPage: int = 1, pageSize: int = 10):
    """Get member awards with pagination"""
    try:
        query = db.query(CoMembersAwards)

        # Filter by member UUID if provided
        if memberid:
            member = db.query(CoMember).filter(CoMember.uuid == memberid).first()
            if member:
                query = query.filter(CoMembersAwards.memberId == member.id)
            else:
                return success_response("No awards found", {
                    "awards": [],
                    "pagination": {
                        "totalCount": 0,
                        "currentPage": perPage,
                        "pageSize": pageSize,
                        "totalPages": 0,
                        "hasNext": False,
                        "hasPrevious": False
                    }
                })

        # Filter by organization UUID if provided
        if organizationid:
            organization = db.query(CoOrganization).filter(CoOrganization.uuid == organizationid).first()
            if organization:
                query = query.filter(CoMembersAwards.organizationId == organization.id)
            else:
                return success_response("No awards found", {
                    "awards": [],
                    "pagination": {
                        "totalCount": 0,
                        "currentPage": perPage,
                        "pageSize": pageSize,
                        "totalPages": 0,
                        "hasNext": False,
                        "hasPrevious": False
                    }
                })

        # Get total count
        total_count = query.count()

        # Get paginated results
        awards = query.offset((perPage - 1) * pageSize).limit(pageSize).all()

        if not awards:
            return success_response("No awards found", {
                "awards": [],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": perPage,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": perPage * pageSize < total_count,
                    "hasPrevious": perPage > 1
                }
            })

        awards_list = []
        for award in awards:
            # Get member and organization UUIDs
            member = db.query(CoMember).filter(CoMember.id == award.memberId).first()
            organization = db.query(CoOrganization).filter(CoOrganization.id == award.organizationId).first()

            if member and organization:
                # Add UUIDs to the award object for validation
                award.memberUuid = member.uuid
                award.organizationUuid = organization.uuid
                awards_list.append(MemberAwardResponse.model_validate(award))

        return success_response(
            "Awards retrieved successfully",
            {
                "awards": awards_list,
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": perPage,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": perPage * pageSize < total_count,
                    "hasPrevious": perPage > 1
                }
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving awards: {str(e)}"}
        )

def get_member_award_by_uuid(uuid: str, db: Session):
    """Get member award by UUID"""
    try:
        award = db.query(CoMembersAwards).filter(CoMembersAwards.uuid == uuid).first()
        
        if not award:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Award with UUID {uuid} not found"}
            )
        
        # Get member and organization UUIDs for response
        member = db.query(CoMember).filter(CoMember.id == award.memberId).first()
        organization = db.query(CoOrganization).filter(CoOrganization.id == award.organizationId).first()

        if member and organization:
            # Add UUIDs to the award object for validation
            award.memberUuid = member.uuid
            award.organizationUuid = organization.uuid
            award_response = MemberAwardResponse.model_validate(award)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": "Associated member or organization not found"}
            )

        return success_response(
            "Award retrieved successfully",
            {"award": award_response}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving award: {str(e)}"}
        )

def update_member_award_by_uuid(uuid: str, award: MemberAwardUpdate, user: dict, db: Session):
    """Update member award by UUID"""
    try:
        existing_award = db.query(CoMembersAwards).filter(CoMembersAwards.uuid == uuid).first()
        
        if not existing_award:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Award with UUID {uuid} not found"}
            )
        
        user_id = user.get("sub") or user.get("id")
        
        # Update fields
        for key, value in award.model_dump(exclude_unset=True).items():
            if hasattr(existing_award, key):
                setattr(existing_award, key, value)
        
        if user_id is not None:
            existing_award.updatedBy = user_id
        db.commit()
        db.refresh(existing_award)
        
        # Get member and organization UUIDs for response
        member = db.query(CoMember).filter(CoMember.id == existing_award.memberId).first()
        organization = db.query(CoOrganization).filter(CoOrganization.id == existing_award.organizationId).first()

        if member and organization:
            # Add UUIDs to the award object for validation
            existing_award.memberUuid = member.uuid
            existing_award.organizationUuid = organization.uuid
            award_response = MemberAwardResponse.model_validate(existing_award)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": "Associated member or organization not found"}
            )

        return success_response(
            "Award updated successfully",
            {"award": award_response}
        )
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error updating award: {str(e)}"}
        )

def delete_member_award_by_uuid(uuid: str, db: Session):
    """Delete member award by UUID"""
    try:
        award = db.query(CoMembersAwards).filter(CoMembersAwards.uuid == uuid).first()
        
        if not award:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Award with UUID {uuid} not found"}
            )
        
        db.delete(award)
        db.commit()
        
        return success_response("Award deleted successfully")
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error deleting award: {str(e)}"}
        )

# ========================
# Relationship Queries
# ========================

def get_organizations_by_member(member_uuid: str, db: Session):
    """Get all organizations for a specific member"""
    try:
        member = db.query(CoMember).filter(CoMember.uuid == member_uuid).first()
        
        if member is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Member with UUID {member_uuid} not found"}
            )
        
        relations = db.query(CoRelationsMembersOrganizations).filter(CoRelationsMembersOrganizations.memberId == member.id).all()
        
        if not relations:
            return success_response(f"No organizations found for member {member.firstName}", {"organizations": []})
        
        organizations_list = []
        
        for org in relations:
            org = db.query(CoOrganization).filter(CoOrganization.id == org.organizationId).first()
            organizations_list.append(OrganizationResponse.model_validate(org))
        
        return success_response(
            f"Organizations for member {member.firstName} retrieved successfully",
            {"organizations": organizations_list}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving organizations: {str(e)}"}
        )

def get_members_by_organization(organizationid: str, db: Session, perPage: int = 1, pageSize: int = 10):
    """Get all members for a specific organization with pagination"""
    try:
        # Get the organization by UUID
        try:
            import uuid
            org_uuid = uuid.UUID(organizationid)
            organization = db.query(CoOrganization).filter(CoOrganization.uuid == org_uuid).first()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": f"Invalid organization UUID: {organizationid}"}
            )
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization with UUID {organizationid} not found"}
            )
        
        # Get member relations for the organization using internal ID
        query = db.query(CoRelationsMembersOrganizations).filter(
            CoRelationsMembersOrganizations.organizationId == organization.id
        )
        
        # Get total count
        total_count = query.count()
        
        # Get paginated results
        relations = query.offset((perPage - 1) * pageSize).limit(pageSize).all()
        
        if not relations:
            return success_response(f"No members found for organization {organizationid}", {
                "members": [],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": perPage,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": perPage * pageSize < total_count,
                    "hasPrevious": perPage > 1
                }
            })
        
        # Get member IDs from relations
        member_ids = [rel.memberId for rel in relations]
        
        # Fetch member details
        members = db.query(CoMember).filter(CoMember.id.in_(member_ids)).all()
        
        # Convert to response format (Note: This uses member schema, not organization schema)
        members_list = []
        for member in members:
            member_dict = {
                "uuid": str(member.uuid),
                "name": f"{member.firstName or ''} {member.lastName or ''}".strip(),
                "username": member.loginEmail,  # Using email as username
                "lastName": member.lastName,
                "email": member.loginEmail,
                "phoneNumber": member.phone
            }
            members_list.append(member_dict)
        
        return success_response(
            f"Members for organization {organizationid} retrieved successfully",
            {
                "members": members_list,
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": perPage,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": perPage * pageSize < total_count,
                    "hasPrevious": perPage > 1
                }
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving members: {str(e)}"}
        )

def get_awards_by_organization(organizationid: str, db: Session):
    """Get all awards for a specific organization"""
    try:
        # Find organization by UUID
        organization = db.query(CoOrganization).filter(CoOrganization.uuid == organizationid).first()
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Organization with UUID {organizationid} not found"}
            )

        # Get awards for this organization
        awards = db.query(CoMembersAwards).filter(CoMembersAwards.organizationId == organization.id).all()

        if not awards:
            return success_response(f"No awards found for organization {organizationid}", {"awards": []})

        awards_list = []
        for award in awards:
            # Get member UUID
            member = db.query(CoMember).filter(CoMember.id == award.memberId).first()
            if member:
                # Add UUIDs to the award object for validation
                award.memberUuid = member.uuid
                award.organizationUuid = organization.uuid
                awards_list.append(MemberAwardResponse.model_validate(award))

        return success_response(
            f"Awards for organization {organizationid} retrieved successfully",
            {"awards": awards_list}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving awards: {str(e)}"}
        )

def get_awards_by_member(memberid: str, db: Session):
    """Get all awards for a specific member"""
    try:
        # Find member by UUID
        member = db.query(CoMember).filter(CoMember.uuid == memberid).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={"message": f"Member with UUID {memberid} not found"}
            )

        # Get awards for this member
        awards = db.query(CoMembersAwards).filter(CoMembersAwards.memberId == member.id).all()

        if not awards:
            return success_response(f"No awards found for member {memberid}", {"awards": []})

        awards_list = []
        for award in awards:
            # Get organization UUID
            organization = db.query(CoOrganization).filter(CoOrganization.id == award.organizationId).first()
            if organization:
                # Add UUIDs to the award object for validation
                award.memberUuid = member.uuid
                award.organizationUuid = organization.uuid
                awards_list.append(MemberAwardResponse.model_validate(award))

        return success_response(
            f"Awards for member {memberid} retrieved successfully",
            {"awards": awards_list}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error retrieving awards: {str(e)}"}
        )

# ========================
# Bulk Operations
# ========================

def bulk_create_organizations(organizations: List[OrganizationCreate], user: dict, db: Session):
    """Bulk create organizations"""
    try:
        user_id = user.get("sub") or user.get("id")
        created_organizations = []
        
        for organization in organizations:
            new_organization = CoOrganization(
                name=organization.name,
                address1=organization.address1,
                address2=organization.address2,
                city=organization.city,
                state=organization.state,
                zip=organization.zip,
                phone=organization.phone,
                annualRevenue=organization.annualRevenue,
                industry=organization.industry,
                yearFounded=organization.yearFounded,
                businessProfileElementId=organization.businessProfileElementId,
                email=organization.email,
                companySize=organization.companySize,
                createdBy=user_id,
                updatedBy=user_id
            )
            
            db.add(new_organization)
            created_organizations.append(new_organization)
        
        db.commit()
        
        # Refresh all created organizations to get their UUIDs
        for org in created_organizations:
            db.refresh(org)
        
        # Use Pydantic to directly validate SQLAlchemy objects
        organizations_list = [OrganizationResponse.model_validate(org) for org in created_organizations]
        
        return created_response(
            f"{len(created_organizations)} organizations created successfully",
            {"organizations": organizations_list}
        )
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error bulk creating organizations: {str(e)}"}
        )

# ========================
# Search Operations
# ========================

def search_organizations(query: str, db: Session, perPage: int = 1, pageSize: int = 10):
    """Search organizations by name, industry, or other fields"""
    try:
        from sqlalchemy import or_
        
        # Build the filter query
        filter_query = db.query(CoOrganization).filter(
            or_(
                CoOrganization.name.ilike(f"%{query}%"),
                CoOrganization.industry.ilike(f"%{query}%"),
                CoOrganization.city.ilike(f"%{query}%"),
                CoOrganization.state.ilike(f"%{query}%")
            )
        )
        
        # Get total count
        total_count = filter_query.count()
        
        # Get paginated results
        organizations = filter_query.offset((perPage - 1) * pageSize).limit(pageSize).all()
        
        if not organizations:
            return success_response("No organizations found", {
                "organizations": [],
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": perPage,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": perPage * pageSize < total_count,
                    "hasPrevious": perPage > 1
                }
            })
        
        # Use Pydantic to directly validate SQLAlchemy objects
        organizations_list = [OrganizationResponse.model_validate(org) for org in organizations]
        
        return success_response(
            "Organizations search completed successfully",
            {
                "organizations": organizations_list,
                "pagination": {
                    "totalCount": total_count,
                    "currentPage": perPage,
                    "pageSize": pageSize,
                    "totalPages": (total_count + pageSize - 1) // pageSize,
                    "hasNext": perPage * pageSize < total_count,
                    "hasPrevious": perPage > 1
                }
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"Error searching organizations: {str(e)}"}
        )
