from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from controller.member_verification_controller import MemberVerificationController
from db.db import get_db
from dependencies.admin_jwt import validate_jwt_token
from schemas.member_verification import MemberVerificationUpsert
from uuid import UUID

router = APIRouter()

member_verification_controller = MemberVerificationController()

@router.get("/total-verified-members", dependencies=[Depends(validate_jwt_token)])
async def get_total_verified_members(db: Session = Depends(get_db)):
    """
    Get total count of verified members
    """
    return member_verification_controller.get_total_verified_members(db)

# Get all member verifications (admin only)
@router.get("/", dependencies=[Depends(validate_jwt_token)])
async def get_all_member_verifications(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    admin_user_payload=Depends(validate_jwt_token)
):
    """
    Get all member verifications with pagination (admin only)
    """
    return member_verification_controller.get_all_member_verifications(db, page, page_size)

# Get verification for a specific member (admin only)
@router.get("/{member_uuid}", dependencies=[Depends(validate_jwt_token)])
async def get_member_verification(
    member_uuid: UUID,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """
    Get verification information for a specific member (admin only)
    """
    return member_verification_controller.get_member_verification(member_uuid, db)

# Upsert member verification (admin only)
@router.post("/", dependencies=[Depends(validate_jwt_token)])
async def upsert_member_verification(
    verification_data: MemberVerificationUpsert,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """
    Create or update member verification (admin only)
    """
    return member_verification_controller.upsert_member_verification(verification_data, admin_user_payload, db)