from sqlalchemy.orm import Session
from services.member_auth_triggers import post_registration_trigger, post_login_trigger

class MemberAuthTriggersController:
    """
    Controller for handling Auth0 post-registration and post-login triggers
    """

    def handle_post_registration(self, request, db: Session):
        """
        Handle post-registration trigger from Auth0
        Exchanges code for tokens, verifies tokens and creates member in database
        """
        return post_registration_trigger(request, db)

    def handle_post_login(self, request, db: Session):
        """
        Handle post-login trigger from Auth0
        Exchanges code for tokens, verifies tokens and returns member info with access token
        """
        return post_login_trigger(request, db)
