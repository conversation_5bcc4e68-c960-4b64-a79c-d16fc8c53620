from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTT<PERSON><PERSON>earer
from dependencies import admin_jwt, member_jwt

def verify_user(credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
    admin_error = None
    member_error = None

    try:
        # Try verifying as admin
        payload = admin_jwt.validate_jwt_token(credentials)
        user_id = payload.get('sub')
        if user_id is None:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Admin token missing id")
        return {"user_type": "admin", "user_id": user_id, "payload": payload}
    except HTTPException as e:
        admin_error = str(e.detail)

    try:
        # Try verifying as member/Auth0
        payload = member_jwt.validate_token(credentials)
        user_id = payload.get('sub')
        if user_id is None:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Member token missing sub")
        return {"user_type": "member", "user_id": user_id, "payload": payload}
    except HTTPException as e:
        member_error = str(e.detail)

    # Provide detailed error information
    error_detail = f"Invalid token for both admin and member. Admin error: {admin_error}. Member error: {member_error}"
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=error_detail)
