#setup SG for instance
resource "aws_security_group" "lambda-md-be-sg" {
  name        = var.lambda_sg_tag_name
  description = var.lambda_sg_description
  vpc_id      = data.terraform_remote_state.vpc.outputs.vpc_id
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["********/16"]
  }
  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "TCP"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = merge(module.label.tags, { Name = var.lambda_sg_tag_name })
}

#kms key creation
resource "aws_kms_key" "lambda-md-be-kms" {
  description = var.lambda_kms_description
  tags = {
    Alias = var.lambda_kms_alias_name
  }
  enable_key_rotation = "true"
}

resource "aws_kms_alias" "lambda-md-be_kms_alias" {
  name          = "alias/${var.lambda_kms_alias_name}"
  target_key_id = aws_kms_key.lambda-md-be-kms.key_id
}
