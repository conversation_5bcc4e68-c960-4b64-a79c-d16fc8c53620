from fastapi import HTTPException
from jose import jwt, JWTError
import requests
from typing import Dict
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
import base64

def pad_base64(data: str) -> bytes:
    # Fixes padding for base64 decoding
    return (data + '=' * (-len(data) % 4)).encode('utf-8')

def get_jwks(jwks_url: str) -> Dict:
    try:
        if not jwks_url:
            raise RuntimeError("JWKS_URL is not set")
        response = requests.get(jwks_url, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch JWKS: {str(e)}")

def get_public_key(kid: str, jwks_url: str):
    jwks = get_jwks(jwks_url)
    available_kids = [key.get("kid") for key in jwks.get("keys", [])]

    for key in jwks["keys"]:
        if key["kid"] == kid:
            n = int.from_bytes(base64.urlsafe_b64decode(pad_base64(key["n"])), byteorder="big")
            e = int.from_bytes(base64.urlsafe_b64decode(pad_base64(key["e"])), byteorder="big")
            public_key = rsa.RSAPublicNumbers(e, n).public_key(backend=default_backend())
            return public_key

    raise HTTPException(status_code=401, detail=f"You are unauthorized to access this resource.")

def validate_jwt_token(token: str, jwks_url: str, issuer: str, audience: str) -> dict:
    try:
        # Validate configuration
        if not jwks_url:
            raise HTTPException(status_code=500, detail="JWKS URL is not configured")
        if not issuer:
            raise HTTPException(status_code=500, detail="Issuer is not configured")
        if not audience:
            raise HTTPException(status_code=500, detail="Audience is not configured")

        unverified_header = jwt.get_unverified_header(token)

        kid = unverified_header.get("kid")
        if not kid:
            raise HTTPException(status_code=401, detail="Invalid token: Missing 'kid' in header")

        public_key = get_public_key(kid, jwks_url)
        pem_key = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

        payload = jwt.decode(
            token,
            pem_key,
            algorithms=["RS256"],
            issuer=issuer,
            audience=audience
        )
        return payload
    except JWTError as e:
        raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")