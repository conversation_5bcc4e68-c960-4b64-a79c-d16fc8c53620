from sqlalchemy import Column, Integer, String, Boolean, TIMESTAMP, ForeignKey, DateTime, func, text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB, UUID
from db.db import Base

class CoMember(Base):
    __tablename__ = "co_members"

    id = Column("id", Integer, primary_key=True, autoincrement=True)
    auth0Id = Column("auth0Id", String(255), nullable=False, unique=True)
    customerIoId = Column("customerIoId", String(255), nullable=True)
    openWaterId = Column("openWaterId", Integer, nullable=True)
    firstName = Column("firstName", String(255), nullable=True)
    lastName = Column("lastName", String(255), nullable=True)
    loginEmail = Column("loginEmail", String(255), nullable=True, unique=True)
    loginEmailVerified = Column("loginEmailVerified", Boolean, nullable=True, default=False)
    identityType = Column("identityType", String(255), nullable=True)
    personalBusinessEmail = Column("personalBusinessEmail", String(255), nullable=True)
    phone = Column("phone", String(255), nullable=True)
    professionalTitle = Column("professionalTitle", String(255), nullable=True)
    membershipTier = Column("membershipTier", String(255), nullable=True, default="LITE")
    communityStatus = Column("communityStatus", String(255), nullable=True, default="unverified")
    hasSeenFirstLoginMessage = Column("hasSeenFirstLoginMessage", Boolean, nullable=True, default=False)
    dateCreated = Column("dateCreated", DateTime, default=func.now())
    dateUpdated = Column("dateUpdated", DateTime, default=func.now(), onupdate=func.now())
    uuid = Column("uuid", UUID(as_uuid=True), server_default=text('gen_random_uuid()'), unique=True, nullable=False)
    createdByMember = Column("createdByMember", UUID(as_uuid=True), nullable=True)
    updatedByMember = Column("updatedByMember", UUID(as_uuid=True), nullable=True)
    createdByAdmin = Column("createdByAdmin", UUID(as_uuid=True), nullable=True)
    updatedByAdmin = Column("updatedByAdmin", UUID(as_uuid=True), nullable=True)

    # Relationships - temporarily commented out to test basic functionality
    # organization_relations = relationship("CoRelationsMembersOrganizations", back_populates="member")
    # awards = relationship("CoMembersAwards", back_populates="member")
    
    # Feature flags and bookmarks relationships
    feature_flags = relationship("CoFeatureFlag", back_populates="member")
    bookmarks = relationship("CoMemberBookmark", back_populates="member")

class CoAuth0User(Base):
    __tablename__ = "co_auth0_users"

    userId = Column("userId", String(255), primary_key=True)
    email = Column("email", String(255), nullable=False)
    emailVerified = Column("emailVerified", Boolean, nullable=False)
    name = Column("name", String(255), nullable=False)
    nickName = Column("nickName", String(255), nullable=False)
    familyName = Column("familyName", String(255), nullable=True)
    givenName = Column("givenName", String(255), nullable=True)
    picture = Column("picture", String, nullable=False)
    identities = Column("identities", JSONB, nullable=False)
    locale = Column("locale", String(255), nullable=True)
    userMetadata = Column("userMetadata", JSONB, nullable=False)
    appMetadata = Column("appMetadata", JSONB, nullable=False)
    lastIp = Column("lastIp", String(255), nullable=False)
    loginsCount = Column("loginsCount", Integer, nullable=False)
    createdAt = Column("createdAt", TIMESTAMP, nullable=False)
    updatedAt = Column("updatedAt", TIMESTAMP, nullable=False)
    lastLogin = Column("lastLogin", TIMESTAMP, nullable=False)
